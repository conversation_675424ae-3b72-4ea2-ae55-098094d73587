import React, { useState, useRef, useCallback, useMemo, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Form,
  FormItem,
  Input,
  Select,
  Textarea,
  ConditionalField,
  Typography,
  Chip,
  IconCard,
  CollapsibleCard,
  FormMultiWrapper,
  DateTimePicker,
  AvatarImageUploader,
} from '@/shared/components/common';
import { Controller } from 'react-hook-form';
import { ConditionType } from '@/shared/hooks/useFieldCondition';
import { z } from 'zod';
import {

  HasPriceDto,
  StringPriceDto,
  CreateProductDto,
  CreateProductResponse,
  ProductDto,
  EventTicketType,
  ProductTypeEnum,
  PriceTypeEnum,
} from '../../types/product.types';
import { useCustomFields } from '../../hooks/useCustomFieldQuery';
import { NotificationUtil } from '@/shared/utils/notification';
import { FieldValues } from 'react-hook-form';
import { FormRef } from '@/shared/components/common/Form/Form';
import MultiFileUpload, { FileWithMetadata } from '@/modules/data/components/MultiFileUpload';
import { useProductImageUpload } from '@/modules/business/hooks/useProductImageUpload';
import CustomFieldRenderer from '../CustomFieldRenderer';
import SimpleCustomFieldSelector from '../SimpleCustomFieldSelector';
import { useQueryClient } from '@tanstack/react-query';
import { PRODUCT_QUERY_KEYS } from '../../hooks/useProductQuery';

// Interface cho response từ backend khi có ảnh
interface ProductWithImagesResponse {
  id: string;
  name: string;
  price: HasPriceDto | StringPriceDto | null;
  typePrice: string;
  description?: string;
  images: Array<{
    key: string;
    position: number;
    url: string;
  }>;
}

interface ProductWithUploadUrlsResponse {
  id: string;
  name: string;
  price: HasPriceDto | StringPriceDto | null;
  typePrice: string;
  description?: string;
  images: Array<{
    key: string;
    position: number;
    url: string;
  }>;
  uploadUrls: {
    productId: string;
    imagesUploadUrls: Array<{
      url: string;
      key: string;
      index: number;
    }>;
  };
  classifications?: Array<{
    id: number;
    type: string;
    price: {
      listPrice: number;
      salePrice: number;
      currency: string;
      value: number;
    };
    customFields: Array<{
      customFieldId: number;
      value: {
        value: string;
      };
    }>;
    imagesMediaTypes: string[];
    images: Array<{
      key: string;
      position: number;
      url: string;
    }>;
    uploadUrls: {
      classificationId: number;
      imagesUploadUrls: Array<{
        url: string;
        key: string;
        index: number;
      }>;
    };
  }>;
}

interface EventProductFormProps {
  onSubmit: (
    values: CreateProductDto
  ) => Promise<
    CreateProductResponse | ProductDto | ProductWithImagesResponse | ProductWithUploadUrlsResponse
  >;
  onCancel: () => void;
  isSubmitting: boolean;
}

// Interface cho trường tùy chỉnh đã chọn
interface SelectedCustomField {
  id: number;
  fieldId: number;
  label: string;
  component: string;
  type: string;
  required: boolean;
  configJson: Record<string, unknown>;
  value: Record<string, unknown>;
}

// Interface cho loại vé trong form (có thêm id và các field khác cho UI)
interface FormEventTicketType extends Partial<EventTicketType> {
  id: string; // ID tạm thời cho quản lý state
  name: string;
  price: number;
  currency?: string;
  totalTickets?: number;
  saleStartTime?: Date;
  saleEndTime?: Date;
  ticketImage?: string;
  sku?: string;
  minQuantityPerOrder?: number;
  maxQuantityPerOrder?: number;
  description?: string;
}

// Interface cho form values
interface EventProductFormValues {
  name: string;
  description?: string;
  tags?: string[];
  customFields?: SelectedCustomField[];
  media?: FileWithMetadata[];
  // Event product specific fields
  eventDateTime?: Date;
  eventLocation?: string;
  attendanceMode: 'ONLINE' | 'OFFLINE';
  zoomLink?: string;
  ticketTypes: FormEventTicketType[];
}

/**
 * Form tạo sự kiện
 */
const EventProductForm: React.FC<EventProductFormProps> = ({ onSubmit, onCancel, isSubmitting }) => {
  const { t } = useTranslation(['business', 'common']);

  // Schema validation cho sự kiện
  const eventProductSchema = z
    .object({
      name: z.string().min(1, 'Tên sự kiện không được để trống'),
      description: z.string().optional(),
      tags: z.array(z.string()).optional(),
      media: z.any().optional(),
      customFields: z.any().optional(),
      // Event product specific validations
      eventDateTime: z.date().optional(),
      eventLocation: z.string().optional(),
      attendanceMode: z.enum(['ONLINE', 'OFFLINE']),
      zoomLink: z.string().url('Link Zoom không hợp lệ').optional().or(z.literal('')),
      ticketTypes: z.array(z.object({
        id: z.string().optional(),
        name: z.string().min(1, 'Tên loại vé không được để trống'),
        price: z.number().min(0, 'Giá vé phải >= 0'),
        currency: z.string().min(1, 'Đơn vị tiền tệ không được để trống'),
        totalTickets: z.number().min(1, 'Tổng số vé phải >= 1'),
        saleStartTime: z.date().optional(),
        saleEndTime: z.date().optional(),
        ticketImage: z.string().optional(),
        sku: z.string().optional(),
        minQuantityPerOrder: z.number().min(1, 'Số vé tối thiểu phải >= 1'),
        maxQuantityPerOrder: z.number().min(1, 'Số vé tối đa phải >= 1'),
        description: z.string().optional(),
      })).min(1, 'Phải có ít nhất 1 loại vé'),
    })
    .superRefine((data, ctx) => {
      // Kiểm tra địa điểm sự kiện cho offline events
      if (data.attendanceMode === 'OFFLINE' && (!data.eventLocation || data.eventLocation.trim() === '')) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Vui lòng nhập địa điểm sự kiện cho hình thức offline',
          path: ['eventLocation'],
        });
      }

      // Kiểm tra Zoom link cho sự kiện online
      if (data.attendanceMode === 'ONLINE' && data.zoomLink && data.zoomLink.trim() !== '') {
        try {
          new URL(data.zoomLink);
        } catch {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: 'Link Zoom không hợp lệ',
            path: ['zoomLink'],
          });
        }
      }

      // Kiểm tra ticket types
      if (!data.ticketTypes || data.ticketTypes.length === 0) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Phải có ít nhất 1 loại vé',
          path: ['ticketTypes'],
        });
      } else {
        data.ticketTypes.forEach((ticket, index) => {
          if (!ticket.name || ticket.name.trim() === '') {
            ctx.addIssue({
              code: z.ZodIssueCode.custom,
              message: `Tên loại vé ${index + 1} không được để trống`,
              path: ['ticketTypes', index, 'name'],
            });
          }
          if (ticket.maxQuantityPerOrder < ticket.minQuantityPerOrder) {
            ctx.addIssue({
              code: z.ZodIssueCode.custom,
              message: `Số vé tối đa phải >= số vé tối thiểu cho loại vé ${index + 1}`,
              path: ['ticketTypes', index, 'maxQuantityPerOrder'],
            });
          }
        });
      }
    });

  // State cho tags
  const [tempTags, setTempTags] = useState<string[]>([]);

  // State cho media
  const [mediaFiles, setMediaFiles] = useState<FileWithMetadata[]>([]);
  const [isUploading, setIsUploading] = useState(false);

  // State cho trường tùy chỉnh của sản phẩm chính
  const [productCustomFields, setProductCustomFields] = useState<SelectedCustomField[]>([]);

  // State cho loại vé sự kiện
  const [ticketTypes, setTicketTypes] = useState<FormEventTicketType[]>([
    {
      id: `ticket-${Date.now()}`,
      name: '',
      price: 0,
      currency: 'VND',
      totalTickets: 1,
      minQuantityPerOrder: 1,
      maxQuantityPerOrder: 10,
    }
  ]);

  // Form ref
  const formRef = useRef<FormRef<Record<string, unknown>>>(null);

  // Đồng bộ ticketTypes state với form field
  useEffect(() => {
    if (formRef.current) {
      formRef.current.setValues({ ticketTypes });
    }
  }, [ticketTypes]);

  // Query lấy danh sách trường tùy chỉnh
  useCustomFields();

  // Hook để upload ảnh sản phẩm theo pattern MediaPage
  const { uploadProductImages } = useProductImageUpload();

  // Query client để invalidate cache sau khi upload xong
  const queryClient = useQueryClient();

  // Xử lý khi submit form
  const handleSubmit = async (values: FieldValues) => {
    console.log('🚀 EventProductForm handleSubmit called with values:', values);

    // Đồng bộ ticketTypes state với form values trước khi validate
    const formValues = {
      ...values,
      ticketTypes: ticketTypes,
    } as EventProductFormValues;

    if (!formValues.name) {
      console.error('❌ Missing required fields:', {
        name: formValues.name,
      });
      NotificationUtil.error({
        message: 'Vui lòng nhập tên sự kiện',
        duration: 3000,
      });
      return;
    }

    // Kiểm tra ticket types
    const validTicketTypes = ticketTypes.filter(ticket => ticket.name.trim() !== '');
    if (validTicketTypes.length === 0) {
      NotificationUtil.error({
        message: 'Vui lòng thêm ít nhất 1 loại vé',
        duration: 3000,
      });
      return;
    }

    try {
      const finalFormValues = formValues as EventProductFormValues;
      setIsUploading(true);

      console.log('✅ Form values before processing:', finalFormValues);

      // Tạo advancedInfo theo API structure cho EVENT product
      const advancedInfo = {
        purchaseCount: 0,
        eventFormat: finalFormValues.attendanceMode === 'ONLINE' ? 'ONLINE' : finalFormValues.attendanceMode === 'OFFLINE' ? 'OFFLINE' : 'HYBRID',
        eventLink: finalFormValues.zoomLink || 'https://zoom.us/j/123456789',
        eventLocation: finalFormValues.eventLocation || 'Trung tâm Hội nghị Quốc gia, Hà Nội',
        startDate: finalFormValues.eventDateTime ? finalFormValues.eventDateTime.getTime() : Date.now(),
        endDate: finalFormValues.eventDateTime ? finalFormValues.eventDateTime.getTime() + (24 * 60 * 60 * 1000) : Date.now() + (24 * 60 * 60 * 1000), // +1 day
        timezone: 'Asia/Ho_Chi_Minh',
        ticketTypes: validTicketTypes.map(ticket => ({
          name: ticket.name,
          price: ticket.price,
          startTime: finalFormValues.eventDateTime ? finalFormValues.eventDateTime.getTime() : Date.now(),
          endTime: finalFormValues.eventDateTime ? finalFormValues.eventDateTime.getTime() + (24 * 60 * 60 * 1000) : Date.now() + (24 * 60 * 60 * 1000),
          timezone: 'Asia/Ho_Chi_Minh',
          description: ticket.description || 'Vé tham gia hội thảo cơ bản',
          quantity: ticket.totalTickets || 100,
          minQuantityPerPurchase: ticket.minQuantityPerOrder || 1,
          maxQuantityPerPurchase: ticket.maxQuantityPerOrder || 5,
          status: 'PENDING' as const,
          // Chỉ thêm imagesMediaTypes nếu ticket có ảnh
          imagesMediaTypes: ticket.ticketImage && typeof ticket.ticketImage === 'string' && ticket.ticketImage.startsWith('blob:')
            ? ['image/jpeg']
            : undefined,
        })) as EventTicketType[],
      };

      const productData: CreateProductDto = {
        name: finalFormValues.name,
        productType: ProductTypeEnum.EVENT,
        price: {
          priceDescription: validTicketTypes.length > 0
            ? `Từ ${Math.min(...validTicketTypes.map(t => t.price)).toLocaleString()} VND`
            : 'Liên hệ'
        } as StringPriceDto,
        typePrice: PriceTypeEnum.STRING_PRICE,
        description: finalFormValues.description || undefined,
        tags: finalFormValues.tags && finalFormValues.tags.length > 0 ? finalFormValues.tags : undefined,
        imagesMediaTypes:
          mediaFiles.length > 0 ? mediaFiles.map(file => file.file.type) : undefined,
        customFields:
          productCustomFields.length > 0
            ? productCustomFields.map(field => ({
                customFieldId: field.fieldId,
                value: field.value,
              }))
            : undefined,
        advancedInfo,
      };

      console.log('📤 Final event product data to be sent to API:', JSON.stringify(productData, null, 2));

      // Gọi callback onSubmit để parent component xử lý API call và nhận response
      const response = await onSubmit(productData);

      console.log('✅ Event product created successfully:', response);

      // Upload media nếu có và API trả về images với upload URLs
      if (mediaFiles.length > 0) {
        try {
          // Kiểm tra xem response có uploadUrls.imagesUploadUrls không
          const hasUploadUrls =
            response &&
            typeof response === 'object' &&
            'uploadUrls' in response &&
            response.uploadUrls &&
            typeof response.uploadUrls === 'object' &&
            'imagesUploadUrls' in response.uploadUrls &&
            Array.isArray(response.uploadUrls.imagesUploadUrls);

          if (hasUploadUrls) {
            const uploadUrls = response.uploadUrls.imagesUploadUrls;

            if (uploadUrls.length > 0) {
              console.log('🚀 Starting image upload with TaskQueue...');

              // Tạo mapping giữa media files và upload URLs từ backend
              const uploadTasks = mediaFiles.slice(0, uploadUrls.length).map((fileData, index) => {
                const uploadInfo = uploadUrls[index];
                return {
                  file: fileData.file,
                  uploadUrl: uploadInfo.url,
                  key: uploadInfo.key,
                  index: uploadInfo.index,
                };
              });

              // Upload tất cả ảnh cùng lúc với Promise.all
              const filesToUpload = uploadTasks.map((task, index) => ({
                file: task.file,
                id: `${Date.now()}_${index}`,
              }));
              const urlsToUpload = uploadTasks.map(task => task.uploadUrl);

              // Upload tất cả ảnh cùng lúc, skip cache invalidation trong hook
              await uploadProductImages(filesToUpload, urlsToUpload, {
                skipCacheInvalidation: true,
              });

              console.log('✅ All event product images uploaded successfully');

              // Invalidate cache để refresh danh sách sản phẩm một lần duy nhất
              queryClient.invalidateQueries({ queryKey: PRODUCT_QUERY_KEYS.lists() });

              NotificationUtil.success({
                message: t(
                  'business:product.mediaUploadSuccess',
                  'Tải lên ảnh sản phẩm thành công'
                ),
                duration: 3000,
              });
            }
          } else {
            console.warn('⚠️ Media files exist but no upload URLs provided from backend');
            NotificationUtil.warning({
              message: t(
                'business:product.mediaUploadWarning',
                'Sản phẩm đã được tạo nhưng không thể tải lên ảnh'
              ),
              duration: 5000,
            });
          }
        } catch (uploadError) {
          console.error('❌ Error uploading event product images:', uploadError);
          NotificationUtil.warning({
            message: t(
              'business:product.mediaUploadError',
              'Có lỗi xảy ra khi tải lên ảnh sản phẩm'
            ),
            duration: 5000,
          });
        }
      }

      // Upload ảnh cho ticket types (classifications) nếu có
      const responseWithClassifications = response as ProductWithUploadUrlsResponse;
      if (responseWithClassifications && responseWithClassifications.classifications && Array.isArray(responseWithClassifications.classifications)) {
        try {
          console.log('🎫 Processing ticket type images upload...');

          // Lọc các ticket types có ảnh cần upload
          const ticketTypesWithImages = ticketTypes.filter(ticket =>
            ticket.ticketImage &&
            typeof ticket.ticketImage === 'string' &&
            ticket.ticketImage.startsWith('blob:')
          );

          if (ticketTypesWithImages.length > 0) {
            console.log(`📸 Found ${ticketTypesWithImages.length} ticket types with images to upload`);

            // Upload ảnh cho từng ticket type
            for (let ticketIndex = 0; ticketIndex < ticketTypesWithImages.length; ticketIndex++) {
              const ticket = ticketTypesWithImages[ticketIndex];

              // Tìm classification tương ứng theo index hoặc theo tên
              // Vì backend tạo classifications theo thứ tự của ticketTypes
              const classification = responseWithClassifications.classifications[ticketIndex] ||
                responseWithClassifications.classifications.find(c =>
                  c.type === ticket.name && c.uploadUrls && c.uploadUrls.classificationId
                );

              if (classification && classification.uploadUrls && classification.uploadUrls.imagesUploadUrls) {
                const uploadUrls = classification.uploadUrls.imagesUploadUrls;

                if (uploadUrls.length > 0 && ticket.ticketImage) {
                  console.log(`🚀 Uploading image for ticket type: ${ticket.name} (Classification ID: ${classification.uploadUrls.classificationId})`);

                  // Lấy file từ blob URL
                  try {
                    const fetchResponse = await fetch(ticket.ticketImage);
                    const blob = await fetchResponse.blob();
                    const file = new File([blob], `ticket-${ticket.id}.jpg`, { type: 'image/jpeg' });

                    // Upload với URL đầu tiên
                    const uploadUrl = uploadUrls[0].url;

                    await uploadProductImages([{ file, id: `ticket-${ticket.id}` }], [uploadUrl], {
                      skipCacheInvalidation: true,
                    });

                    console.log(`✅ Ticket type image uploaded successfully: ${ticket.name}`);
                  } catch (ticketUploadError) {
                    console.error(`❌ Error uploading ticket type image for ${ticket.name}:`, ticketUploadError);
                  }
                }
              } else {
                console.warn(`⚠️ No classification found for ticket type: ${ticket.name}`);
              }
            }

            NotificationUtil.success({
              message: 'Tải lên ảnh vé thành công',
              duration: 3000,
            });
          }
        } catch (classificationUploadError) {
          console.error('❌ Error uploading ticket type images:', classificationUploadError);
          NotificationUtil.warning({
            message: 'Có lỗi xảy ra khi tải lên ảnh vé',
            duration: 5000,
          });
        }
      }

      setIsUploading(false);
    } catch (error) {
      console.error('Error in EventProductForm handleSubmit:', error);
      setIsUploading(false);

      NotificationUtil.error({
        message: t('business:product.createError'),
        duration: 3000,
      });
    }
  };



  // Thêm/xóa trường tùy chỉnh vào sản phẩm chính
  const handleToggleCustomFieldToProduct = useCallback(
    (fieldId: number, fieldData?: Record<string, unknown>) => {
      setProductCustomFields(prev => {
        const existingFieldIndex = prev.findIndex(field => field.fieldId === fieldId);

        if (existingFieldIndex !== -1) {
          return prev.filter((_, index) => index !== existingFieldIndex);
        }

        const newField: SelectedCustomField = {
          id: Date.now(),
          fieldId,
          label: (fieldData?.label as string) || `Field ${fieldId}`,
          component: (fieldData?.component as string) || (fieldData?.type as string) || 'text',
          type: (fieldData?.type as string) || 'text',
          required: (fieldData?.required as boolean) || false,
          configJson: (fieldData?.configJson as Record<string, unknown>) || {},
          value: { value: '' },
        };

        return [...prev, newField];
      });
    },
    []
  );

  // Xóa trường tùy chỉnh khỏi sản phẩm chính
  const handleRemoveCustomFieldFromProduct = useCallback((customFieldId: number) => {
    setProductCustomFields(prev => prev.filter(field => field.id !== customFieldId));
  }, []);

  // Cập nhật giá trị trường tùy chỉnh trong sản phẩm chính
  const handleUpdateCustomFieldInProduct = useCallback((customFieldId: number, value: string) => {
    setProductCustomFields(prev =>
      prev.map(field => {
        if (field.id === customFieldId) {
          return {
            ...field,
            value: { value },
          };
        }
        return field;
      })
    );
  }, []);

  // Thêm loại vé mới
  const handleAddTicketType = useCallback(() => {
    const newTicket: FormEventTicketType = {
      id: `ticket-${Date.now()}`,
      name: '',
      price: 0,
      currency: 'VND',
      totalTickets: 1,
      minQuantityPerOrder: 1,
      maxQuantityPerOrder: 10,
    };
    setTicketTypes(prev => [...prev, newTicket]);
  }, []);

  // Xóa loại vé
  const handleRemoveTicketType = useCallback((ticketId: string) => {
    setTicketTypes(prev => prev.filter(ticket => ticket.id !== ticketId));
  }, []);

  // Cập nhật thông tin loại vé
  const handleUpdateTicketType = useCallback((ticketId: string, field: keyof FormEventTicketType, value: string | number | Date | null | undefined) => {
    setTicketTypes(prev =>
      prev.map(ticket => {
        if (ticket.id === ticketId) {
          return {
            ...ticket,
            [field]: value,
          };
        }
        return ticket;
      })
    );
  }, []);

  // Giá trị mặc định cho form
  const defaultValues = useMemo(
    () => ({
      name: '',
      description: '',
      tags: [],
      customFields: [],
      media: [],
      // Event product defaults
      eventDateTime: undefined,
      eventLocation: '',
      attendanceMode: 'OFFLINE' as const,
      zoomLink: '',
      ticketTypes: [
        {
          id: `ticket-${Date.now()}`,
          name: '',
          price: 0,
          currency: 'VND',
          totalTickets: 1,
          minQuantityPerOrder: 1,
          maxQuantityPerOrder: 10,
        }
      ],
    }),
    []
  );

  return (
    <FormMultiWrapper title={t('business:product.form.createEventTitle', 'Tạo sự kiện')}>
      <Form
        ref={formRef}
        schema={eventProductSchema}
        onSubmit={handleSubmit}
        onError={errors => {
          console.error('🔥 Form validation errors:', errors);
          const firstError = Object.values(errors)[0];
          const errorMessage = firstError?.message || 'Vui lòng kiểm tra lại thông tin đã nhập';
          NotificationUtil.error({
            message: errorMessage,
            duration: 5000,
          });
        }}
        defaultValues={defaultValues}
        submitOnEnter={false}
        className="space-y-4"
      >
        {/* 1. Thông tin chung */}
        <CollapsibleCard
          title={
            <Typography variant="h6" className="font-medium">
              {t('business:product.form.sections.generalInfo', '1. Thông tin chung')}
            </Typography>
          }
          defaultOpen={true}
          className="mb-4"
        >
          <div className="space-y-4">
            <FormItem name="name" label={t('business:product.name')} required>
              <Input fullWidth placeholder="Nhập tên sự kiện" />
            </FormItem>

            <FormItem name="description" label={t('business:product.form.description')}>
              <Textarea
                fullWidth
                rows={4}
                placeholder="Mô tả chi tiết về sự kiện"
              />
            </FormItem>

            <FormItem name="tags" label={t('business:product.tags')}>
              <Controller
                name="tags"
                render={({ field }) => (
                  <div className="space-y-2">
                    <Input
                      fullWidth
                      placeholder="Nhập tag và nhấn Enter"
                      onKeyDown={e => {
                        if (e.key === 'Enter' && e.currentTarget.value.trim()) {
                          e.preventDefault();
                          const newTag = e.currentTarget.value.trim();
                          if (!tempTags.includes(newTag)) {
                            const newTags = [...tempTags, newTag];
                            setTempTags(newTags);
                            field.onChange(newTags);
                          }
                          e.currentTarget.value = '';
                        }
                      }}
                    />
                    <div className="flex flex-wrap gap-1 mt-2">
                      {tempTags.map((tag, tagIndex) => (
                        <Chip
                          key={`tag-${tagIndex}-${tag}`}
                          size="sm"
                          closable
                          onClose={() => {
                            const newTags = tempTags.filter(t => t !== tag);
                            setTempTags(newTags);
                            field.onChange(newTags);
                          }}
                        >
                          {tag}
                        </Chip>
                      ))}
                    </div>
                  </div>
                )}
              />
            </FormItem>
          </div>
        </CollapsibleCard>

        {/* 2. Thông tin tổ chức sự kiện */}
        <CollapsibleCard
          title={
            <Typography variant="h6" className="font-medium">
              2. Thông tin tổ chức sự kiện
            </Typography>
          }
          defaultOpen={true}
          className="mb-4"
        >
          <div className="space-y-4">
            {/* Thời gian sự kiện */}
            <FormItem name="eventDateTime" label="Thời gian sự kiện">
              <Controller
                name="eventDateTime"
                render={({ field }) => (
                  <DateTimePicker
                    value={field.value}
                    onChange={field.onChange}
                    placeholder="Chọn ngày và giờ sự kiện"
                    format="dd/MM/yyyy HH:mm"
                    timeFormat="24h"
                    fullWidth
                    minDate={new Date()}
                  />
                )}
              />
            </FormItem>

            {/* Hình thức tham dự */}
            <FormItem name="attendanceMode" label="Hình thức tham dự" required>
              <Select
                fullWidth
                options={[
                  { value: 'OFFLINE', label: 'Offline (Trực tiếp)' },
                  { value: 'ONLINE', label: 'Online (Trực tuyến)' },
                ]}
              />
            </FormItem>

            {/* Địa điểm sự kiện - chỉ hiển thị cho offline */}
            <ConditionalField
              condition={{
                field: 'attendanceMode',
                type: ConditionType.EQUALS,
                value: 'OFFLINE',
              }}
            >
              <FormItem name="eventLocation" label="Địa điểm sự kiện" required>
                <Input fullWidth placeholder="Nhập địa điểm tổ chức sự kiện" />
              </FormItem>
            </ConditionalField>

            {/* Link Zoom - chỉ hiển thị cho online */}
            <ConditionalField
              condition={{
                field: 'attendanceMode',
                type: ConditionType.EQUALS,
                value: 'ONLINE',
              }}
            >
              <FormItem name="zoomLink" label="Link Zoom">
                <Input fullWidth placeholder="Nhập link Zoom cho sự kiện online" />
              </FormItem>
            </ConditionalField>
          </div>
        </CollapsibleCard>

        {/* 3. Loại vé sự kiện */}
        <CollapsibleCard
          title={
            <Typography variant="h6" className="font-medium">
              3. Loại vé sự kiện
            </Typography>
          }
          defaultOpen={true}
          className="mb-4"
        >
          <div className="space-y-4">
            {/* Danh sách loại vé */}
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <Typography variant="body2" className="font-medium">
                  Danh sách loại vé
                </Typography>
                <IconCard
                  icon="plus"
                  title="Thêm loại vé"
                  onClick={handleAddTicketType}
                  variant="primary"
                  size="sm"
                  className="cursor-pointer"
                />
              </div>

              {ticketTypes.map((ticket, index) => (
                <CollapsibleCard
                  key={ticket.id}
                  title={
                    <div className="flex justify-between items-center w-full">
                      <div className="flex items-center space-x-4">
                        <Typography variant="body2" className="font-medium">
                          {ticket.name || `Loại vé ${index + 1}`}
                        </Typography>
                        <Typography variant="body2" className="text-gray-600 dark:text-gray-400">
                          {ticket.price > 0 ? `${ticket.price.toLocaleString()} ${ticket.currency}` : '0 VND'}
                        </Typography>
                        <Typography variant="body2" className="text-gray-500 dark:text-gray-500">
                          {ticket.totalTickets} vé
                        </Typography>
                        {ticket.id && (
                          <Typography variant="body2" className="text-gray-400 dark:text-gray-600 text-xs">
                            ID: {ticket.id.split('-').pop()}
                          </Typography>
                        )}
                      </div>
                      {ticketTypes.length > 1 && (
                        <IconCard
                          icon="trash"
                          title="Xóa loại vé"
                          onClick={() => handleRemoveTicketType(ticket.id!)}
                          variant="secondary"
                          size="sm"
                          className="cursor-pointer"
                        />
                      )}
                    </div>
                  }
                  defaultOpen={true}
                >
                  <div className="space-y-4">
                    {/* Tên loại vé và giá */}
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <FormItem label="Tên loại vé" required>
                        <Input
                          fullWidth
                          placeholder="VIP, Thường, Sinh viên..."
                          value={ticket.name}
                          onChange={(e) => handleUpdateTicketType(ticket.id!, 'name', e.target.value)}
                        />
                      </FormItem>
                      <FormItem label="Giá" required>
                        <Input
                          fullWidth
                          type="number"
                          min="0"
                          placeholder="0"
                          value={ticket.price}
                          onChange={(e) => handleUpdateTicketType(ticket.id!, 'price', Number(e.target.value))}
                        />
                      </FormItem>
                      <FormItem label="Đơn vị tiền tệ" required>
                        <Select
                          fullWidth
                          value={ticket.currency}
                          onChange={(value) => handleUpdateTicketType(ticket.id!, 'currency', Array.isArray(value) ? value[0] : value)}
                          options={[
                            { value: 'VND', label: 'VND' },
                            { value: 'USD', label: 'USD' },
                            { value: 'EUR', label: 'EUR' },
                          ]}
                        />
                      </FormItem>
                    </div>

                    {/* Tổng số vé và SKU */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <FormItem label="Tổng số vé" required>
                        <Input
                          fullWidth
                          type="number"
                          min="1"
                          placeholder="100"
                          value={ticket.totalTickets}
                          onChange={(e) => handleUpdateTicketType(ticket.id!, 'totalTickets', Number(e.target.value))}
                        />
                      </FormItem>
                      <FormItem label="Mã SKU">
                        <Input
                          fullWidth
                          placeholder="VIP-001"
                          value={ticket.sku || ''}
                          onChange={(e) => handleUpdateTicketType(ticket.id!, 'sku', e.target.value)}
                        />
                      </FormItem>
                    </div>

                    {/* Thời gian bán vé */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <FormItem label="Thời gian bắt đầu bán">
                        <DateTimePicker
                          value={ticket.saleStartTime}
                          onChange={(date) => handleUpdateTicketType(ticket.id!, 'saleStartTime', date)}
                          placeholder="Chọn ngày và giờ bắt đầu bán"
                          format="dd/MM/yyyy HH:mm"
                          timeFormat="24h"
                          fullWidth
                          minDate={new Date()}
                        />
                      </FormItem>
                      <FormItem label="Thời gian kết thúc bán">
                        <DateTimePicker
                          value={ticket.saleEndTime}
                          onChange={(date) => handleUpdateTicketType(ticket.id!, 'saleEndTime', date)}
                          placeholder="Chọn ngày và giờ kết thúc bán"
                          format="dd/MM/yyyy HH:mm"
                          timeFormat="24h"
                          fullWidth
                          minDate={new Date()}
                        />
                      </FormItem>
                    </div>

                    {/* Số lượng mua tối thiểu/tối đa */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <FormItem label="Số vé tối thiểu/lần mua" required>
                        <Input
                          fullWidth
                          type="number"
                          min="1"
                          placeholder="1"
                          value={ticket.minQuantityPerOrder}
                          onChange={(e) => handleUpdateTicketType(ticket.id!, 'minQuantityPerOrder', Number(e.target.value))}
                        />
                      </FormItem>
                      <FormItem label="Số vé tối đa/lần mua" required>
                        <Input
                          fullWidth
                          type="number"
                          min="1"
                          placeholder="10"
                          value={ticket.maxQuantityPerOrder}
                          onChange={(e) => handleUpdateTicketType(ticket.id!, 'maxQuantityPerOrder', Number(e.target.value))}
                        />
                      </FormItem>
                    </div>

                    {/* Hình ảnh vé */}
                    <FormItem label="Hình ảnh vé">
                      <AvatarImageUploader
                        value={ticket.ticketImage || ''}
                        onChange={(imageUrl) => handleUpdateTicketType(ticket.id!, 'ticketImage', imageUrl)}
                        placeholder="Chưa có hình ảnh vé"
                        size="lg"
                        shape="square"
                      />
                    </FormItem>

                    {/* Mô tả loại vé */}
                    <FormItem label="Mô tả loại vé">
                      <Textarea
                        fullWidth
                        rows={3}
                        placeholder="Mô tả chi tiết về loại vé này..."
                        value={ticket.description || ''}
                        onChange={(e) => handleUpdateTicketType(ticket.id!, 'description', e.target.value)}
                      />
                    </FormItem>
                  </div>
                </CollapsibleCard>
              ))}
            </div>
          </div>
        </CollapsibleCard>

        {/* 4. Hình ảnh sự kiện */}
        <CollapsibleCard
          title={
            <Typography variant="h6" className="font-medium">
              {t('business:product.form.sections.media', '4. Hình ảnh sự kiện')}
            </Typography>
          }
          defaultOpen={false}
          className="mb-4"
        >
          <div className="space-y-4">
            <FormItem name="media" label={t('business:product.form.media')}>
              <Controller
                name="media"
                render={({ field }) => (
                  <MultiFileUpload
                    value={mediaFiles}
                    onChange={(files: FileWithMetadata[]) => {
                      setMediaFiles(files);
                      field.onChange(files);
                    }}
                    accept="image/*"
                    mediaOnly={true}
                    placeholder={t(
                      'business:product.form.mediaPlaceholder',
                      'Kéo thả hoặc click để tải lên ảnh/video'
                    )}
                    className="w-full"
                  />
                )}
              />
            </FormItem>
          </div>
        </CollapsibleCard>

        {/* 5. Trường tùy chỉnh */}
        <CollapsibleCard
          title={
            <Typography variant="h6" className="font-medium">
              {t('business:product.form.sections.customFields', '5. Trường tùy chỉnh')}
            </Typography>
          }
          defaultOpen={false}
          className="mb-4"
        >
          <div className="space-y-4">
            <SimpleCustomFieldSelector
              onFieldSelect={fieldData => {
                handleToggleCustomFieldToProduct(
                  fieldData.id,
                  fieldData as unknown as Record<string, unknown>
                );
              }}
              selectedFieldIds={productCustomFields.map(field => field.fieldId)}
            />

            {productCustomFields.length > 0 && (
              <div className="space-y-4 mt-4">
                <Typography variant="body2" className="font-medium">
                  {t('business:product.form.selectedCustomFields', 'Trường tùy chỉnh đã chọn:')}
                </Typography>
                {productCustomFields.map(field => (
                  <CustomFieldRenderer
                    key={field.id}
                    field={field}
                    value={field.value as unknown as string | number | boolean}
                    onChange={(value: string | number | boolean) =>
                      handleUpdateCustomFieldInProduct(field.id, String(value))
                    }
                    onRemove={() => handleRemoveCustomFieldFromProduct(field.id)}
                  />
                ))}
              </div>
            )}
          </div>
        </CollapsibleCard>

        {/* Actions */}
        <div className="flex justify-end space-x-4 pt-6">
          <IconCard
            icon="x"
            title={t('common:cancel')}
            onClick={onCancel}
            variant="secondary"
            className="cursor-pointer"
          />
          <IconCard
            icon="check"
            title={
              isSubmitting || isUploading
                ? t('business:product.form.creating', 'Đang tạo...')
                : t('business:product.form.create', 'Tạo sự kiện')
            }
            onClick={() => formRef.current?.submit()}
            variant="primary"
            disabled={isSubmitting || isUploading}
            className="cursor-pointer"
          />
        </div>
      </Form>
    </FormMultiWrapper>
  );
};

export default EventProductForm;
