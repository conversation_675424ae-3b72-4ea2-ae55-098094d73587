import React, { useState } from 'react';
import {
  Typo<PERSON>,
  <PERSON><PERSON>,
  Card,
} from '@/shared/components/common';
import CustomerImport from './CustomerImport';

/**
 * Demo component để test CustomerImport với tab và checkbox mới
 */
const CustomerImportDemo: React.FC = () => {
  const [isImportVisible, setIsImportVisible] = useState(false);

  const handleImportComplete = (importedCount: number) => {
    console.log(`Import completed: ${importedCount} customers imported`);
    setIsImportVisible(false);
  };

  return (
    <div className="w-full bg-background text-foreground p-6">
      <div className="max-w-4xl mx-auto">
        <Typography variant="h3" className="mb-4">
          Customer Import Demo
        </Typography>
        
        <Typography variant="body1" className="mb-6 text-muted">
          Demo component để test việc cải thiện tab và checkbox trong CustomerImport
        </Typography>

        <Card className="p-6">
          <Typography variant="h5" className="mb-4">
            C<PERSON>i thiện đã thực hiện:
          </Typography>
          
          <ul className="space-y-2 mb-6">
            <li className="flex items-center space-x-2">
              <span className="w-2 h-2 bg-green-500 rounded-full"></span>
              <Typography variant="body2">
                ✅ Thay thế tab tự tạo bằng component Tabs của hệ thống với type="segmented"
              </Typography>
            </li>
            <li className="flex items-center space-x-2">
              <span className="w-2 h-2 bg-green-500 rounded-full"></span>
              <Typography variant="body2">
                ✅ Thay thế checkbox thô bằng component Checkbox của hệ thống
              </Typography>
            </li>
            <li className="flex items-center space-x-2">
              <span className="w-2 h-2 bg-green-500 rounded-full"></span>
              <Typography variant="body2">
                ✅ Sử dụng fullWidth cho Input components
              </Typography>
            </li>
            <li className="flex items-center space-x-2">
              <span className="w-2 h-2 bg-green-500 rounded-full"></span>
              <Typography variant="body2">
                ✅ Thêm translation keys đầy đủ cho đa ngôn ngữ
              </Typography>
            </li>
            <li className="flex items-center space-x-2">
              <span className="w-2 h-2 bg-green-500 rounded-full"></span>
              <Typography variant="body2">
                ✅ Đưa Import form vào Card component
              </Typography>
            </li>
            <li className="flex items-center space-x-2">
              <span className="w-2 h-2 bg-green-500 rounded-full"></span>
              <Typography variant="body2">
                ✅ Di chuyển Button đóng xuống dưới form (không có icon)
              </Typography>
            </li>
            <li className="flex items-center space-x-2">
              <span className="w-2 h-2 bg-green-500 rounded-full"></span>
              <Typography variant="body2">
                ✅ Tabs được căn giữa với alignment="center"
              </Typography>
            </li>
            <li className="flex items-center space-x-2">
              <span className="w-2 h-2 bg-green-500 rounded-full"></span>
              <Typography variant="body2">
                ✅ Tất cả step components có full width layout
              </Typography>
            </li>
            <li className="flex items-center space-x-2">
              <span className="w-2 h-2 bg-green-500 rounded-full"></span>
              <Typography variant="body2">
                ✅ Cải thiện UI/UX với design system nhất quán
              </Typography>
            </li>
            <li className="flex items-center space-x-2">
              <span className="w-2 h-2 bg-green-500 rounded-full"></span>
              <Typography variant="body2">
                ✅ Thêm checkbox "File có dòng tiêu đề" với variant="filled"
              </Typography>
            </li>
            <li className="flex items-center space-x-2">
              <span className="w-2 h-2 bg-green-500 rounded-full"></span>
              <Typography variant="body2">
                ✅ Đặt variant="filled" làm mặc định cho Checkbox component
              </Typography>
            </li>
            <li className="flex items-center space-x-2">
              <span className="w-2 h-2 bg-green-500 rounded-full"></span>
              <Typography variant="body2">
                ✅ Thêm state management cho checkbox options
              </Typography>
            </li>
          </ul>

          <Button 
            onClick={() => setIsImportVisible(true)}
            variant="primary"
          >
            Mở Customer Import
          </Button>
        </Card>

        {/* Customer Import Component */}
        {isImportVisible && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-background rounded-lg shadow-lg max-w-4xl w-full max-h-[90vh] overflow-hidden">
              <CustomerImport
                onClose={() => setIsImportVisible(false)}
                onImportComplete={handleImportComplete}
              />
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default CustomerImportDemo;
