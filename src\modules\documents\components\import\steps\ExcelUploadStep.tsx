import React, { useState, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import * as XLSX from 'xlsx';
import {
  Typography,
  Button,
  Input,
  Icon,
  Tabs,
  Checkbox,
} from '@/shared/components/common';
import { ExcelData, ImportFromUrlData } from '../../../types/customer-import.types';

interface ExcelUploadStepProps {
  activeTab: 'file' | 'url';
  onTabChange: (tab: 'file' | 'url') => void;
  onExcelUploaded: (data: ExcelData) => void;
}

/**
 * Component cho bước upload Excel file hoặc từ URL
 */
const ExcelUploadStep: React.FC<ExcelUploadStepProps> = ({
  activeTab,
  onTabChange,
  onExcelUploaded,
}) => {
  const { t } = useTranslation(['business', 'common']);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // State cho upload từ file
  const [isDragOver, setIsDragOver] = useState(false);
  const [isUploading, setIsUploading] = useState(false);

  // State cho upload từ file
  const [fileHasHeader, setFileHasHeader] = useState(true);

  // State cho upload từ URL
  const [urlData, setUrlData] = useState<ImportFromUrlData>({
    url: '',
    hasHeader: true,
    sheetName: '',
  });
  const [isLoadingFromUrl, setIsLoadingFromUrl] = useState(false);

  // Xử lý parse Excel file
  const parseExcelFile = (file: File, hasHeader: boolean = true): Promise<ExcelData> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();

      reader.onload = (e) => {
        try {
          const data = new Uint8Array(e.target?.result as ArrayBuffer);
          const workbook = XLSX.read(data, { type: 'array' });

          // Lấy sheet đầu tiên
          const sheetName = workbook.SheetNames[0];
          const worksheet = workbook.Sheets[sheetName];

          // Chuyển đổi thành JSON với header
          const jsonData = XLSX.utils.sheet_to_json(worksheet, {
            header: 1,
            defval: null
          }) as (string | number | boolean | null)[][];

          if (jsonData.length === 0) {
            reject(new Error(t('customer.import.errors.emptyFile')));
            return;
          }

          let headers: string[];
          let rows: (string | number | boolean | null)[][];

          if (hasHeader) {
            // Lấy headers từ dòng đầu tiên
            headers = (jsonData[0] || []).map(header =>
              header ? String(header).trim() : ''
            );
            // Lấy data từ dòng thứ 2 trở đi
            rows = jsonData.slice(1);
          } else {
            // Tạo headers tự động: Column 1, Column 2, ...
            const columnCount = jsonData[0]?.length || 0;
            headers = Array.from({ length: columnCount }, (_, i) => `Column ${i + 1}`);
            // Lấy tất cả data từ dòng đầu tiên
            rows = jsonData;
          }

          resolve({
            headers,
            rows,
            fileName: file.name,
          });
        } catch {
          reject(new Error(t('customer.import.errors.invalidFile')));
        }
      };

      reader.onerror = () => {
        reject(new Error(t('customer.import.errors.readError')));
      };

      reader.readAsArrayBuffer(file);
    });
  };

  // Xử lý upload file
  const handleFileUpload = async (file: File) => {
    if (!file) return;

    // Kiểm tra định dạng file
    const allowedTypes = [
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // .xlsx
      'application/vnd.ms-excel', // .xls
      'text/csv', // .csv
    ];

    if (!allowedTypes.includes(file.type)) {
      alert(t('customer.import.errors.invalidFileType'));
      return;
    }

    // Kiểm tra kích thước file (max 10MB)
    if (file.size > 10 * 1024 * 1024) {
      alert(t('customer.import.errors.fileTooLarge'));
      return;
    }

    setIsUploading(true);

    try {
      console.log('Parsing file:', file.name, 'hasHeader:', fileHasHeader);
      const excelData = await parseExcelFile(file, fileHasHeader);
      console.log('Parsed excel data:', excelData);
      onExcelUploaded(excelData);
    } catch (error) {
      console.error('Parse error:', error);
      alert(error instanceof Error ? error.message : t('customer.import.errors.parseError'));
    } finally {
      setIsUploading(false);
    }
  };

  // Xử lý drag & drop
  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);

    const files = Array.from(e.dataTransfer.files);
    if (files.length > 0) {
      handleFileUpload(files[0]);
    }
  };



  // Xử lý thay đổi file input
  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    console.log('File input changed:', e.target.files);
    const files = e.target.files;
    if (files && files.length > 0) {
      console.log('Selected file:', files[0]);
      handleFileUpload(files[0]);
    }
  };

  // Xử lý load từ URL
  const handleLoadFromUrl = async () => {
    if (!urlData.url.trim()) {
      alert(t('customer.import.errors.urlRequired'));
      return;
    }

    setIsLoadingFromUrl(true);

    try {
      // Fetch file từ URL
      const response = await fetch(urlData.url);
      if (!response.ok) {
        throw new Error(t('customer.import.errors.urlFetchError'));
      }

      const blob = await response.blob();
      const file = new File([blob], 'imported-file.xlsx', {
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      });

      const excelData = await parseExcelFile(file, urlData.hasHeader);
      onExcelUploaded(excelData);
    } catch (error) {
      alert(error instanceof Error ? error.message : t('customer.import.errors.urlLoadError'));
    } finally {
      setIsLoadingFromUrl(false);
    }
  };

  return (
    <div className="w-full space-y-6">
      <div className="w-full text-center">
        <Typography variant="h5" className="mb-2">
          {t('customer.import.upload.title')}
        </Typography>
        <Typography variant="body2" className="text-muted">
          {t('customer.import.upload.description')}
        </Typography>
      </div>

      <div className="w-full space-y-6">
        <Tabs
          type="segmented"
          activeKey={activeTab}
          onChange={(key) => onTabChange(key as 'file' | 'url')}
          className="w-full"
          contentClassName="w-full"
          alignment="center"
          items={[
            {
              key: 'file',
              label: t('customer.import.upload.fromFile'),
              icon: <Icon name="upload" size="sm" />,
              children: (
                <div className="w-full mt-6">
                  {/* File Upload Content */}
                  <div
                    className={`
                      w-full border-2 border-dashed rounded-lg p-8 text-center transition-colors cursor-pointer
                      ${isDragOver ? 'border-primary bg-primary/5' : 'border-border'}
                      ${isUploading ? 'opacity-50 pointer-events-none' : 'hover:border-primary/50'}
                    `}
                    onDrop={handleDrop}
                    onDragOver={handleDragOver}
                    onDragLeave={handleDragLeave}
                    onClick={() => {
                      console.log('Drag area clicked, isUploading:', isUploading);
                      if (!isUploading) {
                        console.log('Triggering file input click');
                        fileInputRef.current?.click();
                      }
                    }}
                  >
                    <input
                      ref={fileInputRef}
                      type="file"
                      accept=".xlsx,.xls,.csv"
                      onChange={handleFileInputChange}
                      className="hidden"
                    />

                    <div className="space-y-4 pointer-events-none">
                      <div className="mx-auto w-12 h-12 bg-muted/50 rounded-full flex items-center justify-center">
                        <Icon name="upload" size="lg" className="text-muted-foreground" />
                      </div>

                      <div>
                        <Typography variant="h6" className="mb-2">
                          Kéo thả file vào đây hoặc click để chọn
                        </Typography>
                        <Typography variant="body2" className="text-muted mb-4">
                          Hỗ trợ file Excel (.xlsx, .xls) và CSV
                        </Typography>
                      </div>

                      <Typography variant="body2" className="text-muted text-xs">
                        {t('customer.import.upload.supportedFormats')}
                      </Typography>
                    </div>
                  </div>

                  {/* File Header Checkbox */}
                  <div className="w-full mt-4">
                    <Checkbox
                      label={t('customer.import.upload.hasHeader')}
                      checked={fileHasHeader}
                      onChange={setFileHasHeader}
                    />
                  </div>
                </div>
              ),
            },
            {
              key: 'url',
              label: t('customer.import.upload.fromUrl'),
              icon: <Icon name="link" size="sm" />,
              children: (
                <div className="w-full mt-6">
                  {/* URL Upload Content */}
                  <div className="w-full space-y-4">
                    <div className="w-full">
                      <Typography variant="body2" className="mb-2 font-medium">
                        {t('customer.import.upload.excelUrl')}
                      </Typography>
                      <Input
                        type="url"
                        placeholder={t('customer.import.upload.urlPlaceholder')}
                        value={urlData.url}
                        onChange={(e) => setUrlData(prev => ({ ...prev, url: e.target.value }))}
                        fullWidth
                      />
                    </div>

                    <div className="w-full">
                      <Typography variant="body2" className="mb-2 font-medium">
                        {t('customer.import.upload.sheetName')}
                      </Typography>
                      <Input
                        type="text"
                        placeholder={t('customer.import.upload.sheetNamePlaceholder')}
                        value={urlData.sheetName}
                        onChange={(e) => setUrlData(prev => ({ ...prev, sheetName: e.target.value }))}
                        fullWidth
                      />
                      <Typography variant="body2" className="text-muted text-xs mt-1">
                        {t('customer.import.upload.sheetNameHelp')}
                      </Typography>
                    </div>

                    <div className="w-full">
                      <Checkbox
                        label={t('customer.import.upload.hasHeader')}
                        checked={urlData.hasHeader}
                        onChange={(checked) => setUrlData(prev => ({ ...prev, hasHeader: checked }))}
                      />
                    </div>

                    <div className="w-full">
                      <Button
                        onClick={handleLoadFromUrl}
                        disabled={!urlData.url || isLoadingFromUrl}
                        className="w-full"
                      >
                        {isLoadingFromUrl ? (
                          <>
                            <Icon name="loader" size="sm" className="mr-2 animate-spin" />
                            {t('customer.import.upload.loading')}
                          </>
                        ) : (
                          <>
                            <Icon name="download" size="sm" className="mr-2" />
                            {t('customer.import.upload.loadFromUrl')}
                          </>
                        )}
                      </Button>
                    </div>
                  </div>
                </div>
              ),
            },
          ]}
        />
      </div>
    </div>
  );
};

export default ExcelUploadStep;
