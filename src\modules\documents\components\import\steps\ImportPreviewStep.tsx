import React, { useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Typography,
  Button,
  Table,
  Icon,
  Checkbox,
} from '@/shared/components/common';
import { ListOverviewCard } from '@/shared/components/widgets';
import { TableColumn } from '@/shared/components/common/Table/types';
import {
  ExcelData,
  ColumnMapping,
  ImportedCustomerData,
} from '../../../types/customer-import.types';

interface ImportPreviewStepProps {
  excelData: ExcelData;
  mappings: ColumnMapping[];
  onStartImport: () => void;
  onGoBack: () => void;
}

/**
 * Component cho bước preview dữ liệu trước khi import
 */
const ImportPreviewStep: React.FC<ImportPreviewStepProps> = ({
  excelData,
  mappings,
  onStartImport,
  onGoBack,
}) => {
  const { t } = useTranslation(['business', 'common']);

  // State cho import options
  const [skipInvalidRows, setSkipInvalidRows] = useState(true);
  const [sendWelcomeEmail, setSendWelcomeEmail] = useState(true);

  // Transform data theo mappings
  const transformedData = useMemo(() => {
    // Add null check for mappings and excelData
    if (!mappings || !Array.isArray(mappings) || !excelData || !excelData.rows) {
      return [];
    }
    const activeMappings = mappings.filter(m => m.customerField);

    return excelData.rows.map((row, index) => {
      const customerData: ImportedCustomerData = {
        rowIndex: index + 2, // +2 vì bắt đầu từ dòng 2 (sau header)
      };

      activeMappings.forEach(mapping => {
        const columnIndex = excelData.headers.indexOf(mapping.excelColumn);
        const value = row[columnIndex];

        switch (mapping.customerField) {
          case 'name':
            customerData.name = value ? String(value).trim() : undefined;
            break;
          case 'email':
            customerData.email = value ? String(value).trim() : undefined;
            break;
          case 'phone':
            customerData.phone = value ? String(value).trim() : undefined;
            break;
          case 'address':
            customerData.address = value ? String(value).trim() : undefined;
            break;
          case 'tags':
            if (value) {
              customerData.tags = String(value).split(',').map(tag => tag.trim()).filter(tag => tag);
            }
            break;
          default:
            // Custom fields
            if (!customerData.customFields) {
              customerData.customFields = {};
            }
            customerData.customFields[mapping.customerField] = value;
            break;
        }
      });

      return customerData;
    });
  }, [excelData, mappings]);

  // Validation data
  const validationResult = useMemo(() => {
    const validData: ImportedCustomerData[] = [];
    const invalidData: ImportedCustomerData[] = [];
    const errors: string[] = [];

    transformedData.forEach(data => {
      const rowErrors: string[] = [];

      // Validate required fields
      if (!data.name || data.name.trim() === '') {
        rowErrors.push(t('customer.import.validation.nameRequired'));
      }

      // Validate email format
      if (data.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(data.email)) {
        rowErrors.push(t('customer.import.validation.invalidEmail'));
      }

      // Validate phone format (basic)
      if (data.phone && !/^[\d\s\-+()]+$/.test(data.phone)) {
        rowErrors.push(t('customer.import.validation.invalidPhone'));
      }

      if (rowErrors.length > 0) {
        invalidData.push(data);
        errors.push(`Dòng ${data.rowIndex}: ${rowErrors.join(', ')}`);
      } else {
        validData.push(data);
      }
    });

    return {
      validData,
      invalidData,
      errors,
      totalRows: transformedData.length,
      validRows: validData.length,
      invalidRows: invalidData.length,
    };
  }, [transformedData, t]);

  // Columns cho preview table
  const previewColumns: TableColumn<ImportedCustomerData>[] = [
    {
      key: 'rowIndex',
      title: t('customer.import.preview.row'),
      dataIndex: 'rowIndex',
      width: 80,
    },
    {
      key: 'name',
      title: t('customer.form.name'),
      dataIndex: 'name',
      render: (value: unknown) => value ? String(value) : <span className="text-muted">-</span>,
    },
    {
      key: 'email',
      title: t('customer.form.email'),
      dataIndex: 'email',
      render: (value: unknown) => value ? String(value) : <span className="text-muted">-</span>,
    },
    {
      key: 'phone',
      title: t('customer.form.phone'),
      dataIndex: 'phone',
      render: (value: unknown) => value ? String(value) : <span className="text-muted">-</span>,
    },
    {
      key: 'address',
      title: t('customer.form.address'),
      dataIndex: 'address',
      render: (value: unknown) => value ? String(value) : <span className="text-muted">-</span>,
    },
    {
      key: 'tags',
      title: t('customer.form.tags'),
      dataIndex: 'tags',
      render: (value: unknown) => {
        const tags = value as string[] | undefined;
        return tags && tags.length > 0 ? tags.join(', ') : <span className="text-muted">-</span>;
      },
    },
  ];

  return (
    <div className="w-full space-y-6">
      {/* Header */}
      <div className="w-full text-center">
        <Typography variant="h5" className="mb-2">
          {t('customer.import.preview.title')}
        </Typography>
        <Typography variant="body2" className="text-muted">
          {t('customer.import.preview.description')}
        </Typography>
      </div>

      {/* Summary */}
      <ListOverviewCard
        items={[
          {
            title: t('customer.import.preview.totalRows'),
            value: validationResult.totalRows,
            color: 'blue',
            description: 'Tổng số dòng'
          },
          {
            title: t('customer.import.preview.validRows'),
            value: validationResult.validRows,
            color: 'green',
            description: 'Dòng hợp lệ'
          },
          {
            title: t('customer.import.preview.invalidRows'),
            value: validationResult.invalidRows,
            color: 'red',
            description: 'Dòng lỗi'
          }
        ]}
        maxColumns={{ xs: 1, sm: 3, md: 3, lg: 3, xl: 3 }}
        gap={4}
      />

      {/* Validation Errors */}
      {validationResult.errors.length > 0 && (
        <div className="p-4 border border-orange-200 bg-orange-50 rounded-lg">
          <div className="flex items-start space-x-2">
            <Icon name="alert-triangle" size="sm" className="text-orange-500 mt-0.5" />
            <div className="flex-1">
              <Typography variant="body2" className="font-medium text-orange-700 mb-2">
                {t('customer.import.preview.validationWarnings')}
              </Typography>
              <div className="max-h-32 overflow-y-auto">
                <ul className="space-y-1">
                  {validationResult.errors.slice(0, 10).map((error, index) => (
                    <li key={index} className="text-sm text-orange-600">
                      • {error}
                    </li>
                  ))}
                  {validationResult.errors.length > 10 && (
                    <li className="text-sm text-orange-600">
                      ... và {validationResult.errors.length - 10} lỗi khác
                    </li>
                  )}
                </ul>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Preview Table */}
      <div className="p-6 bg-card rounded-lg">
        <Typography variant="h6" className="mb-4">
          {t('customer.import.preview.dataPreview')}
        </Typography>
        <div className="overflow-x-auto">
          <Table
            columns={previewColumns}
            data={validationResult.validData.slice(0, 10)}
            rowKey="rowIndex"
            pagination={false}
            size="sm"
          />
        </div>
        {validationResult.validData.length > 10 && (
          <Typography variant="body2" className="text-muted text-center mt-2">
            {t('customer.import.preview.showingFirst10')}
          </Typography>
        )}
      </div>

      {/* Import Options */}
      <div className="p-4 bg-card rounded-lg">
        <Typography variant="body2" className="font-medium mb-4">
          {t('customer.import.preview.importOptions')}
        </Typography>
        <div className="space-y-4">
          <div>
            <Checkbox
              label={t('customer.import.preview.skipInvalidRows')}
              checked={skipInvalidRows}
              onChange={setSkipInvalidRows}
            />
          </div>
          <div>
            <Checkbox
              label={t('customer.import.preview.sendWelcomeEmail')}
              checked={sendWelcomeEmail}
              onChange={setSendWelcomeEmail}
            />
          </div>
        </div>
      </div>

      {/* Actions */}
      <div className="flex justify-between">
        <Button variant="outline" onClick={onGoBack}>
          {t('common.back')}
        </Button>
        
        <Button 
          onClick={onStartImport}
          disabled={validationResult.validRows === 0}
          variant="primary"
        >
          <Icon name="upload" size="sm" className="mr-2" />
          {t('customer.import.preview.startImport')} ({validationResult.validRows})
        </Button>
      </div>
    </div>
  );
};

export default ImportPreviewStep;
