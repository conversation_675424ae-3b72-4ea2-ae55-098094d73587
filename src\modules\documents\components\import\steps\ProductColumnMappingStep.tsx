import React, { useState, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Typography,
  Table,
  Icon,
  Select,
  Button,
} from '@/shared/components/common';
import { TableColumn } from '@/shared/components/common/Table/types';
import {
  ProductExcelData,
  ProductColumnMapping,
  ProductField,
} from '../../../types/product-import.types';
import { DEFAULT_PRODUCT_FIELDS, PRODUCT_COLUMN_AUTO_MAPPING } from '../../../constants/product-import.constants';
import { useCustomFields } from '../../../hooks/useCustomFieldQuery';
import { CustomFieldListItem } from '../../../services/custom-field.service';

interface ProductColumnMappingStepProps {
  excelData: ProductExcelData;
  onMappingComplete: (mappings: ProductColumnMapping[]) => void;
  mappings?: ProductColumnMapping[];
  onGoBack?: () => void;
}

/**
 * Component cho bước mapping columns Excel với trường sản phẩm
 */
const ProductColumnMappingStep: React.FC<ProductColumnMappingStepProps> = ({
  excelData,
  onMappingComplete,
  mappings: existingMappings,
  onGoBack,
}) => {
  const { t } = useTranslation(['common', 'business']);

  // Lấy custom fields từ API
  const { data: customFieldsData } = useCustomFields({
    page: 1,
    limit: 100, // Lấy tất cả custom fields
  });

  // Helper function to map custom field type to ProductField type
  const mapCustomFieldType = (customType: string): ProductField['type'] => {
    switch (customType.toLowerCase()) {
      case 'string':
      case 'text':
        return 'text';
      case 'number':
        return 'number';
      case 'boolean':
        return 'boolean';
      case 'textarea':
        return 'textarea';
      case 'select':
        return 'select';
      default:
        return 'text'; // Default fallback
    }
  };

  // Danh sách trường sản phẩm có thể map (bao gồm custom fields)
  const productFields: ProductField[] = useMemo(() => {
    const defaultFields = [...DEFAULT_PRODUCT_FIELDS];

    // Thêm custom fields nếu có
    if (customFieldsData?.items) {
      const customFields: ProductField[] = customFieldsData.items.map((field: CustomFieldListItem) => ({
        key: `custom_${field.id}`,
        label: `${field.label} (Custom)`,
        type: mapCustomFieldType(field.type),
        required: field.required,
      }));

      return [...defaultFields, ...customFields];
    }

    return defaultFields;
  }, [customFieldsData]);

  // State cho mappings
  const [mappings, setMappings] = useState<ProductColumnMapping[]>(() => {
    // Nếu đã có mappings từ props thì sử dụng
    if (existingMappings && existingMappings.length > 0) {
      return existingMappings;
    }

    // Auto-mapping dựa trên tên cột
    return excelData.headers.map(header => {
      const lowerHeader = header.toLowerCase().trim();
      let productField = '';

      // Auto-detect common field names using constants
      productField = PRODUCT_COLUMN_AUTO_MAPPING[lowerHeader] || '';

      return {
        excelColumn: header,
        productField,
      };
    });
  });

  // Cập nhật mapping cho một cột
  const updateMapping = (excelColumn: string, productField: string) => {
    setMappings(prev => prev.map(mapping => 
      mapping.excelColumn === excelColumn
        ? { ...mapping, productField }
        : mapping
    ));
  };

  // Kiểm tra validation tổng thể - chỉ validate trường bắt buộc cơ bản, không validate custom fields
  const globalValidationErrors = useMemo(() => {
    const errors: string[] = [];

    // Chỉ kiểm tra trường bắt buộc cơ bản (không phải custom fields)
    const requiredFields = productFields.filter(f => f.required && !f.key.startsWith('custom_'));
    requiredFields.forEach(field => {
      const isMapped = mappings.some(m => m.productField === field.key);
      if (!isMapped) {
        errors.push(t('business:product.import.mapping.errors.requiredFieldMissing', { field: field.label }));
      }
    });

    return errors;
  }, [mappings, productFields, t]);

  // Kiểm tra xem có thể tiếp tục không
  const canProceed = useMemo(() => {
    // Kiểm tra có ít nhất một mapping hợp lệ
    const validMappings = mappings.filter(m => m.productField);
    if (validMappings.length === 0) return false;

    // Kiểm tra không có duplicate mapping
    const mappedProductFields = validMappings.map(m => m.productField);
    const uniqueFields = new Set(mappedProductFields);
    if (mappedProductFields.length !== uniqueFields.size) return false;

    // Chỉ kiểm tra trường bắt buộc cơ bản (không phải custom fields)
    const requiredFields = productFields.filter(f => f.required && !f.key.startsWith('custom_'));
    const mappedRequiredFields = requiredFields.filter(field =>
      mappings.some(m => m.productField === field.key)
    );

    return mappedRequiredFields.length === requiredFields.length;
  }, [mappings, productFields]);

  // Handle continue to next step
  const handleContinue = () => {
    if (canProceed) {
      onMappingComplete(mappings);
    }
  };


  // Columns cho table preview
  const previewColumns: TableColumn<Record<string, unknown>>[] = [
    {
      key: 'index',
      title: '#',
      render: (_, __, index) => index + 1,
      width: 50,
    },
    ...excelData.headers.map(header => ({
      key: header,
      title: header,
      dataIndex: header,
      render: (value: unknown) => {
        const strValue = value ? String(value) : '';
        return strValue.length > 50 ? `${strValue.substring(0, 50)}...` : strValue;
      },
    })),
  ];

  // Data cho table preview (tất cả dữ liệu để có phân trang)
  const previewData = useMemo(() => {
    return excelData.rows.map((row, index) => {
      const rowData: Record<string, unknown> = { id: index };
      excelData.headers.forEach((header, headerIndex) => {
        rowData[header] = row[headerIndex];
      });
      return rowData;
    });
  }, [excelData]);

  return (
    <div className="w-full space-y-6">
      {/* Header */}
      <div className="w-full text-center">
        <Typography variant="h5" className="mb-2">
          {t('business:product.import.mapping.title')}
        </Typography>
        <Typography variant="body2" className="text-muted">
          {t('business:product.import.mapping.description')}
        </Typography>
      </div>

      {/* File info */}
      <div className="p-4 bg-muted/50 rounded-lg">
        <div className="flex items-center space-x-4">
          <Icon name="file-text" size="sm" />
          <div>
            <Typography variant="body2" className="font-medium">
              {excelData.fileName}
            </Typography>
            <Typography variant="body2" className="text-muted text-xs">
              {excelData.headers.length} cột, {excelData.rows.length} dòng
            </Typography>
          </div>
        </div>
      </div>

      {/* Column Mapping */}
      <div className="p-6 bg-card rounded-lg">
        <Typography variant="h6" className="mb-4">
          {t('business:product.import.mapping.columnMapping')}
        </Typography>

        <div className="space-y-4">
          {excelData.headers.map(header => {
            const mapping = mappings.find(m => m.excelColumn === header);
            const selectedField = productFields.find(f => f.key === mapping?.productField);
            
            // Kiểm tra duplicate mapping
            const isDuplicate = mapping?.productField && 
              mappings.filter(m => m.productField === mapping.productField).length > 1;

            return (
              <div key={header} className="flex items-center space-x-4 p-3 bg-muted/30 rounded-lg">
                <div className="flex-1">
                  <Typography variant="body2" className="font-medium">
                    {header}
                  </Typography>
                  <Typography variant="body2" className="text-muted text-xs">
                    Excel Column
                  </Typography>
                </div>

                <Icon name="arrow-right" size="sm" className="text-muted" />

                <div className="flex-1">
                  <Select
                    value={mapping?.productField || ''}
                    onChange={(value) => updateMapping(header, value as string)}
                    placeholder={t('business:product.import.mapping.skipColumn')}
                    error={isDuplicate ? t('business:product.import.mapping.errors.duplicateMapping') : undefined}
                    options={[
                      { value: '', label: t('business:product.import.mapping.skipColumn') },
                      ...productFields.map(field => ({
                        value: field.key,
                        label: `${field.label}${field.required && !field.key.startsWith('custom_') ? ' *' : ''}`
                      }))
                    ]}
                  />
                  {selectedField?.required && !selectedField.key.startsWith('custom_') && !isDuplicate && (
                    <Typography variant="body2" className="text-xs text-orange-600 mt-1">
                      {t('business:product.import.mapping.requiredField')}
                    </Typography>
                  )}
                </div>
              </div>
            );
          })}
        </div>
      </div>

      {/* Preview Data */}
      <div className="p-6 bg-card rounded-lg">
        <Typography variant="h6" className="mb-4">
          {t('business:product.import.mapping.dataPreview')}
        </Typography>
        <Table
          columns={previewColumns}
          data={previewData}
          rowKey="id"
          pagination={true}
          size="sm"
        />
      </div>

      {/* Global Validation Errors */}
      {globalValidationErrors.length > 0 && (
        <div className="p-4 border border-red-200 bg-red-50 rounded-lg">
          <div className="flex items-start space-x-2">
            <Icon name="alert-circle" size="sm" className="text-red-500 mt-0.5" />
            <div>
              <Typography variant="body2" className="font-medium text-red-700 mb-2">
                {t('business:product.import.mapping.validationErrors')}
              </Typography>
              <ul className="space-y-1">
                {globalValidationErrors.map((error: string, index: number) => (
                  <li key={index} className="text-sm text-red-600">
                    • {error}
                  </li>
                ))}
              </ul>
            </div>
          </div>
        </div>
      )}

      {/* Action Buttons */}
      <div className="flex items-center justify-between p-6 border-t border-border">
        <Button
          variant="outline"
          onClick={onGoBack}
          disabled={!onGoBack}
        >
          {t('common:previous')}
        </Button>

        <Button
          onClick={handleContinue}
          disabled={!canProceed}
          className="ml-auto"
        >
          {t('common:continue')}
        </Button>
      </div>
    </div>
  );
};

export default ProductColumnMappingStep;
