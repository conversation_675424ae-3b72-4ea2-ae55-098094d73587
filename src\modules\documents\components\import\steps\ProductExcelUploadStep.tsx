import React, { useState, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import * as XLSX from 'xlsx';
import {
  Typography,
  Button,
  Input,
  Icon,
  Tabs,
  Checkbox,
} from '@/shared/components/common';
import { ProductExcelData, ProductImportUrlData } from '../../../types/product-import.types';
import { useUploadProductFile, useUploadProductFileFromUrl } from '../../../hooks/useProductImport';

interface ProductExcelUploadStepProps {
  activeTab: 'file' | 'url';
  onTabChange: (tab: 'file' | 'url') => void;
  onExcelUploaded: (data: ProductExcelData) => void;
}

/**
 * Component cho bước upload Excel file hoặc từ URL cho sản phẩm
 */
const ProductExcelUploadStep: React.FC<ProductExcelUploadStepProps> = ({
  activeTab,
  onTabChange,
  onExcelUploaded,
}) => {
  const { t } = useTranslation(['business', 'common']);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // API hooks
  const uploadFileMutation = useUploadProductFile();
  const uploadUrlMutation = useUploadProductFileFromUrl();

  // State cho upload từ file
  const [isDragOver, setIsDragOver] = useState(false);
  const [fileHasHeader, setFileHasHeader] = useState(true);

  // State cho upload từ URL
  const [urlData, setUrlData] = useState<ProductImportUrlData>({
    url: '',
    hasHeader: true,
  });

  // Computed loading states
  const isUploading = uploadFileMutation.isPending;
  const isLoadingFromUrl = uploadUrlMutation.isPending;

  // Xử lý parse Excel file
  const parseExcelFile = (file: File, hasHeader: boolean = true): Promise<ProductExcelData> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();

      reader.onload = (e) => {
        try {
          const data = new Uint8Array(e.target?.result as ArrayBuffer);
          const workbook = XLSX.read(data, { type: 'array' });

          // Lấy sheet đầu tiên
          const sheetName = workbook.SheetNames[0];
          const worksheet = workbook.Sheets[sheetName];

          // Chuyển đổi thành JSON với header
          const jsonData = XLSX.utils.sheet_to_json(worksheet, {
            header: 1,
            defval: null
          }) as (string | number | boolean | null)[][];

          if (jsonData.length === 0) {
            reject(new Error(t('product.import.errors.emptyFile')));
            return;
          }

          let headers: string[];
          let rows: (string | number | boolean | null)[][];

          if (hasHeader) {
            // Lấy headers từ dòng đầu tiên
            headers = (jsonData[0] || []).map(header =>
              header ? String(header).trim() : ''
            );
            // Lấy data từ dòng thứ 2 trở đi
            rows = jsonData.slice(1);
          } else {
            // Tạo headers tự động: Column 1, Column 2, ...
            const columnCount = jsonData[0]?.length || 0;
            headers = Array.from({ length: columnCount }, (_, i) => `Column ${i + 1}`);
            // Lấy tất cả data từ dòng đầu tiên
            rows = jsonData;
          }

          resolve({
            headers,
            rows,
            fileName: file.name,
          });
        } catch {
          reject(new Error(t('product.import.errors.invalidFile')));
        }
      };

      reader.onerror = () => {
        reject(new Error(t('product.import.errors.readError')));
      };

      reader.readAsArrayBuffer(file);
    });
  };

  // Xử lý upload file
  const handleFileUpload = async (file: File) => {
    if (!file) return;

    // Kiểm tra định dạng file
    const allowedTypes = [
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // .xlsx
      'application/vnd.ms-excel', // .xls
      'text/csv', // .csv
    ];

    if (!allowedTypes.includes(file.type)) {
      alert(t('product.import.errors.invalidFileType'));
      return;
    }

    // Kiểm tra kích thước file (max 10MB)
    if (file.size > 10 * 1024 * 1024) {
      alert(t('product.import.errors.fileTooLarge'));
      return;
    }

    try {
      console.log('Uploading product file:', file.name, 'hasHeader:', fileHasHeader);

      // Try API upload first, fallback to local parsing if API fails
      try {
        const response = await uploadFileMutation.mutateAsync({ file, hasHeader: fileHasHeader });
        console.log('API upload response:', response);
        onExcelUploaded(response.excelData);
      } catch (apiError) {
        console.warn('API upload failed, falling back to local parsing:', apiError);
        // Fallback to local parsing
        const excelData = await parseExcelFile(file, fileHasHeader);
        console.log('Local parsed product excel data:', excelData);
        onExcelUploaded(excelData);
      }
    } catch (error) {
      console.error('Product upload error:', error);
      alert(error instanceof Error ? error.message : t('business:product.import.errors.parseError'));
    }
  };

  // Xử lý drag & drop
  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);

    const files = Array.from(e.dataTransfer.files);
    if (files.length > 0) {
      handleFileUpload(files[0]);
    }
  };

  // Xử lý thay đổi file input
  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    console.log('Product file input changed:', e.target.files);
    const files = e.target.files;
    if (files && files.length > 0) {
      console.log('Selected product file:', files[0]);
      handleFileUpload(files[0]);
    }
  };

  // Xử lý load từ URL
  const handleLoadFromUrl = async () => {
    if (!urlData.url.trim()) {
      alert(t('business:product.import.errors.urlRequired'));
      return;
    }

    try {
      console.log('Loading product file from URL:', urlData);

      // Try API upload first, fallback to local fetch if API fails
      try {
        const response = await uploadUrlMutation.mutateAsync(urlData);
        console.log('API URL upload response:', response);
        onExcelUploaded(response.excelData);
      } catch (apiError) {
        console.warn('API URL upload failed, falling back to local fetch:', apiError);
        // Fallback to local fetch and parsing
        const response = await fetch(urlData.url);
        if (!response.ok) {
          throw new Error(t('business:product.import.errors.urlFetchError'));
        }

        const blob = await response.blob();
        const file = new File([blob], 'imported-products.xlsx', {
          type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        });

        const excelData = await parseExcelFile(file, urlData.hasHeader);
        onExcelUploaded(excelData);
      }
    } catch (error) {
      console.error('Product URL load error:', error);
      alert(error instanceof Error ? error.message : t('business:product.import.errors.urlLoadError'));
    }
  };

  return (
    <div className="w-full space-y-6">
      <div className="w-full text-center">
        <Typography variant="h5" className="mb-2">
          {t('business:product.import.upload.title')}
        </Typography>
        <Typography variant="body2" className="text-muted">
          {t('business:product.import.upload.description')}
        </Typography>
      </div>

      <div className="w-full space-y-6">
        <Tabs
          type="segmented"
          activeKey={activeTab}
          onChange={(key) => onTabChange(key as 'file' | 'url')}
          className="w-full"
          contentClassName="w-full"
          alignment="center"
          items={[
            {
              key: 'file',
              label: t('business:product.import.upload.fromFile'),
              icon: <Icon name="upload" size="sm" />,
              children: (
                <div className="w-full mt-6">
                  {/* File Upload Content */}
                  <div
                    className={`
                      w-full border-2 border-dashed rounded-lg p-8 text-center transition-colors cursor-pointer
                      ${isDragOver ? 'border-primary bg-primary/5' : 'border-border'}
                      ${isUploading ? 'opacity-50 pointer-events-none' : 'hover:border-primary/50'}
                    `}
                    onDrop={handleDrop}
                    onDragOver={handleDragOver}
                    onDragLeave={handleDragLeave}
                    onClick={() => {
                      console.log('Product drag area clicked, isUploading:', isUploading);
                      if (!isUploading) {
                        console.log('Triggering product file input click');
                        fileInputRef.current?.click();
                      }
                    }}
                  >
                    <input
                      ref={fileInputRef}
                      type="file"
                      accept=".xlsx,.xls,.csv"
                      onChange={handleFileInputChange}
                      className="hidden"
                    />

                    <div className="space-y-4 pointer-events-none">
                      <div className="mx-auto w-12 h-12 bg-muted/50 rounded-full flex items-center justify-center">
                        <Icon name="upload" size="lg" className="text-muted-foreground" />
                      </div>

                      <div>
                        <Typography variant="h6" className="mb-2">
                          Kéo thả file sản phẩm vào đây hoặc click để chọn
                        </Typography>
                        <Typography variant="body2" className="text-muted mb-4">
                          Hỗ trợ file Excel (.xlsx, .xls) và CSV với thông tin sản phẩm
                        </Typography>
                      </div>

                      <Typography variant="body2" className="text-muted text-xs">
                        {t('business:product.import.upload.supportedFormats')}
                      </Typography>
                    </div>
                  </div>

                  {/* File Header Checkbox */}
                  <div className="w-full mt-4">
                    <Checkbox
                      label={t('business:product.import.upload.hasHeader')}
                      checked={fileHasHeader}
                      onChange={setFileHasHeader}
                    />
                  </div>
                </div>
              ),
            },
            {
              key: 'url',
              label: t('business:product.import.upload.fromUrl'),
              icon: <Icon name="link" size="sm" />,
              children: (
                <div className="w-full mt-6">
                  {/* URL Upload Content */}
                  <div className="w-full space-y-4">
                    <div className="w-full">
                      <Typography variant="body2" className="mb-2 font-medium">
                        {t('business:product.import.upload.excelUrl')}
                      </Typography>
                      <Input
                        type="url"
                        placeholder={t('business:product.import.upload.urlPlaceholder')}
                        value={urlData.url}
                        onChange={(e) => setUrlData(prev => ({ ...prev, url: e.target.value }))}
                        fullWidth
                      />
                    </div>

                    <div className="w-full">
                      <Checkbox
                        label={t('business:product.import.upload.hasHeader')}
                        checked={urlData.hasHeader}
                        onChange={(checked) => setUrlData(prev => ({ ...prev, hasHeader: checked }))}
                      />
                    </div>

                    <div className="w-full">
                      <Button
                        onClick={handleLoadFromUrl}
                        disabled={!urlData.url || isLoadingFromUrl}
                        className="w-full"
                      >
                        {isLoadingFromUrl ? (
                          <>
                            <Icon name="loader" size="sm" className="mr-2 animate-spin" />
                            {t('business:product.import.upload.loading')}
                          </>
                        ) : (
                          <>
                            <Icon name="download" size="sm" className="mr-2" />
                            {t('business:product.import.upload.loadFromUrl')}
                          </>
                        )}
                      </Button>
                    </div>
                  </div>
                </div>
              ),
            },
          ]}
        />
      </div>
    </div>
  );
};

export default ProductExcelUploadStep;
