import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Typography,
  Button,
  Icon,
} from '@/shared/components/common';
import {
  ProductExcelData,
  ProductColumnMapping,
  ProductImportData,
  ProductImportProgress,
  BatchCreateProductDto,
  BatchCreateProductResponse,
} from '../../../types/product-import.types';
import { batchCreateProductsWithBusinessLogic } from '../../../services/product-import.service';

interface ProductImportProgressStepProps {
  excelData: ProductExcelData;
  mappings: ProductColumnMapping[];
  importProgress?: ProductImportProgress;
  onImportComplete: (importedCount: number, errorCount: number) => void;
  onClose: () => void;
}

/**
 * Component cho bước import progress và kết quả sản phẩm
 */
const ProductImportProgressStep: React.FC<ProductImportProgressStepProps> = ({
  excelData,
  mappings,
  onImportComplete,
  onClose,
}) => {
  const { t } = useTranslation(['business', 'common']);

  // State cho progress simulation
  const [progress, setProgress] = useState(0);
  const [currentRow, setCurrentRow] = useState(0);
  const [importedCount, setImportedCount] = useState(0);
  const [errorCount, setErrorCount] = useState(0);
  const [errors, setErrors] = useState<string[]>([]);
  const [isImporting, setIsImporting] = useState(false);
  const [isComplete, setIsComplete] = useState(false);

  // Transform data theo mappings
  const transformData = (row: (string | number | boolean | null)[]): ProductImportData => {
    const activeMappings = mappings.filter(m => m.productField);
    const productData: ProductImportData = {
      name: '',
      typePrice: 'HAS_PRICE',
      price: null,
    };

    let listPrice = 0;
    let salePrice = 0;
    const tags: string[] = [];

    activeMappings.forEach(mapping => {
      const columnIndex = excelData.headers.indexOf(mapping.excelColumn);
      const value = row[columnIndex];

      switch (mapping.productField) {
        case 'name':
          productData.name = value ? String(value).trim() : '';
          break;
        case 'listPrice':
          listPrice = value ? Number(value) || 0 : 0;
          break;
        case 'salePrice':
          salePrice = value ? Number(value) || 0 : 0;
          break;
        case 'description':
          productData.description = value ? String(value).trim() : undefined;
          break;
        case 'tags': {
          const tagValue = value ? String(value).trim() : '';
          if (tagValue) {
            tags.push(...tagValue.split(',').map(tag => tag.trim()).filter(tag => tag));
          }
          break;
        }
        case 'widthCm':
        case 'heightCm':
        case 'lengthCm':
        case 'weightGram': {
          if (!productData.shipmentConfig) {
            productData.shipmentConfig = {};
          }
          const numValue = value ? Number(value) || undefined : undefined;
          if (numValue !== undefined) {
            productData.shipmentConfig[mapping.productField] = numValue;
          }
          break;
        }
        default:
          // Custom fields - check if it's a custom field
          if (mapping.productField.startsWith('custom_')) {
            if (!productData.customFields) {
              productData.customFields = [];
            }
            // Extract custom field ID from mapping (assuming format: custom_123)
            const customFieldId = parseInt(mapping.productField.replace('custom_', ''));
            if (!isNaN(customFieldId)) {
              productData.customFields.push({
                customFieldId,
                value: {
                  value: value ? String(value) : '',
                },
              });
            }
          }
          break;
      }
    });

    // Set price based on listPrice and salePrice
    if (listPrice > 0 || salePrice > 0) {
      productData.price = {
        listPrice: listPrice || salePrice,
        salePrice: salePrice || listPrice,
        currency: 'VND',
      };
      productData.typePrice = 'HAS_PRICE';
    } else {
      productData.price = null;
      productData.typePrice = 'NO_PRICE';
    }

    // Set tags if any
    if (tags.length > 0) {
      productData.tags = tags;
    }

    return productData;
  };

  // Real import process using API
  const performImport = async () => {
    setIsImporting(true);
    const totalRows = excelData.rows.length;

    try {
      // Transform all rows to product data
      const products: ProductImportData[] = [];
      const validationErrors: string[] = [];

      excelData.rows.forEach((row, index) => {
        try {
          const productData = transformData(row);

          // Basic validation
          if (!productData.name || productData.name.trim() === '') {
            validationErrors.push(`Dòng ${index + 2}: Thiếu tên sản phẩm`);
            return;
          }

          if (productData.typePrice === 'HAS_PRICE' && (!productData.price ||
              (typeof productData.price === 'object' && 'listPrice' in productData.price &&
               productData.price.listPrice <= 0))) {
            validationErrors.push(`Dòng ${index + 2}: Giá sản phẩm không hợp lệ`);
            return;
          }

          products.push(productData);
        } catch (error) {
          validationErrors.push(`Dòng ${index + 2}: Lỗi xử lý dữ liệu - ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
      });

      // Update progress to 30% after validation
      setProgress(30);
      setCurrentRow(totalRows);
      setErrors(validationErrors);

      if (products.length === 0) {
        throw new Error('Không có sản phẩm hợp lệ để import');
      }

      // Call batch create API
      const batchRequest: BatchCreateProductDto = { products };
      const response: BatchCreateProductResponse = await batchCreateProductsWithBusinessLogic(batchRequest);

      // Update final results
      setProgress(100);
      setImportedCount(response.successCount);
      setErrorCount(response.failedCount + validationErrors.length);

      // Combine validation errors with API errors
      const allErrors = [
        ...validationErrors,
        ...response.failedProducts.map(failed =>
          `Dòng ${failed.index + 2}: ${failed.error}`
        )
      ];
      setErrors(allErrors);

      setIsImporting(false);
      setIsComplete(true);
      onImportComplete(response.successCount, response.failedCount + validationErrors.length);

    } catch (error) {
      console.error('Import failed:', error);
      setIsImporting(false);
      setIsComplete(true);
      setProgress(100);
      setImportedCount(0);
      setErrorCount(totalRows);
      setErrors([`Lỗi import: ${error instanceof Error ? error.message : 'Unknown error'}`]);
      onImportComplete(0, totalRows);
    }
  };

  // Start import when component mounts
  useEffect(() => {
    if (!isComplete && !isImporting) {
      performImport();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Render importing state
  if (!isComplete) {
    return (
      <div className="w-full space-y-6">
        <div className="w-full text-center">
          <Typography variant="h5" className="mb-2">
            {t('business:product.import.progress.importing')}
          </Typography>
          <Typography variant="body2" className="text-muted">
            {t('business:product.import.progress.pleaseWait')}
          </Typography>
        </div>

        <div className="p-6 bg-card rounded-lg">
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <Typography variant="body2">
                {t('business:product.import.progress.processing')}
              </Typography>
              <Typography variant="body2" className="text-muted">
                {currentRow} / {excelData.rows.length}
              </Typography>
            </div>

            <div className="w-full bg-muted rounded-full h-2">
              <div
                className="bg-primary h-2 rounded-full transition-all duration-300"
                style={{ width: `${progress}%` }}
              />
            </div>

            <div className="grid grid-cols-2 gap-4 text-center">
              <div>
                <Typography variant="h4" className="text-green-600">
                  {importedCount}
                </Typography>
                <Typography variant="body2" className="text-muted">
                  {t('business:product.import.progress.imported')}
                </Typography>
              </div>
              <div>
                <Typography variant="h4" className="text-red-600">
                  {errorCount}
                </Typography>
                <Typography variant="body2" className="text-muted">
                  {t('business:product.import.progress.errors')}
                </Typography>
              </div>
            </div>
          </div>
        </div>

        {errors.length > 0 && (
          <div className="p-4 border border-red-200 bg-red-50 rounded-lg">
            <Typography variant="body2" className="font-medium text-red-700 mb-2">
              {t('product.import.progress.recentErrors')}
            </Typography>
            <div className="max-h-32 overflow-y-auto">
              <ul className="space-y-1">
                {errors.slice(-5).map((error, index) => (
                  <li key={index} className="text-sm text-red-600">
                    • {error}
                  </li>
                ))}
              </ul>
            </div>
          </div>
        )}
      </div>
    );
  }

  // Render complete state
  return (
    <div className="w-full space-y-6">
      <div className="w-full text-center">
        <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
          <Icon name="check" size="lg" className="text-green-600" />
        </div>
        <Typography variant="h5" className="mb-2">
          {t('business:product.import.complete.title')}
        </Typography>
        <Typography variant="body2" className="text-muted">
          {t('business:product.import.complete.description')}
        </Typography>
      </div>

      {/* Summary */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="p-4 text-center bg-card rounded-lg">
          <Typography variant="h4" className="text-blue-600 mb-1">
            {excelData.rows.length}
          </Typography>
          <Typography variant="body2" className="text-muted">
            {t('business:product.import.complete.totalProcessed')}
          </Typography>
        </div>

        <div className="p-4 text-center border border-green-200 bg-green-50 rounded-lg">
          <Typography variant="h4" className="text-green-600 mb-1">
            {importedCount}
          </Typography>
          <Typography variant="body2" className="text-green-700">
            {t('business:product.import.complete.successfullyImported')}
          </Typography>
        </div>

        <div className="p-4 text-center border border-red-200 bg-red-50 rounded-lg">
          <Typography variant="h4" className="text-red-600 mb-1">
            {errorCount}
          </Typography>
          <Typography variant="body2" className="text-red-700">
            {t('business:product.import.complete.failed')}
          </Typography>
        </div>
      </div>

      {/* Error Details */}
      {errors.length > 0 && (
        <div className="p-4 bg-card rounded-lg">
          <Typography variant="body2" className="font-medium mb-2">
            {t('business:product.import.complete.errorDetails')}
          </Typography>
          <div className="max-h-40 overflow-y-auto">
            <ul className="space-y-1">
              {errors.map((error, index) => (
                <li key={index} className="text-sm text-red-600">
                  • {error}
                </li>
              ))}
            </ul>
          </div>
        </div>
      )}

      {/* Next Steps */}
      <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
        <Typography variant="body2" className="font-medium text-blue-700 mb-2">
          {t('business:product.import.complete.nextSteps')}
        </Typography>
        <ul className="space-y-1 text-sm text-blue-600">
          <li>• {t('business:product.import.complete.reviewProducts')}</li>
          <li>• {t('business:product.import.complete.updateInventory')}</li>
          <li>• {t('business:product.import.complete.setupCategories')}</li>
        </ul>
      </div>

      {/* Actions */}
      <div className="flex justify-center space-x-4">
        <Button variant="outline" onClick={onClose}>
          {t('common:close')}
        </Button>
        <Button onClick={() => window.location.reload()}>
          {t('business:product.import.complete.viewProducts')}
        </Button>
      </div>
    </div>
  );
};

export default ProductImportProgressStep;
