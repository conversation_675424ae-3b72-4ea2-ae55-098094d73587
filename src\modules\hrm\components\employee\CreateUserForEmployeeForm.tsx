import React from 'react';
import { FieldValues } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { z } from 'zod';

import { Button, Card, Checkbox, Form, FormGrid, FormItem, Icon, Input, Typography } from '@/shared/components/common';
import { FormRef } from '@/shared/components/common/Form/Form';
import AsyncSelectWithPagination from '@/shared/components/common/Select/AsyncSelectWithPagination';
import { useFormErrors } from '@/shared/hooks';
import { NotificationUtil } from '@/shared/utils/notification';

import { useCreateUserForEmployee } from '../../hooks/useEmployeeUser';
import { useEmployee, loadEmployeesForAsyncSelect } from '../../hooks/useEmployees';
import { createUserForEmployeeSchema } from '../../schemas/employee-user.schema';

// Type cho form values
type CreateUserFormValues = z.infer<ReturnType<typeof createUserForEmployeeSchema>>;

// Props cho component
interface CreateUserForEmployeeFormProps {
  onSuccess?: () => void;
  onCancel?: () => void;
  employeeId?: number;
}

/**
 * Form tạo tài khoản người dùng cho nhân viên
 */
const CreateUserForEmployeeForm: React.FC<CreateUserForEmployeeFormProps> = ({
  onSuccess,
  onCancel,
  employeeId,
}) => {
  const { t } = useTranslation(['hrm', 'common']);
  const { formRef, setFormErrors } = useFormErrors<CreateUserFormValues>();
  const createUserMutation = useCreateUserForEmployee();
  const [showPassword, setShowPassword] = React.useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = React.useState(false);
  const [autoGeneratePassword, setAutoGeneratePassword] = React.useState(false);

  // Lấy thông tin nhân viên nếu có employeeId
  const { data: employeeData } = useEmployee(employeeId || 0);

  // Schema cho form
  const schema = createUserForEmployeeSchema(t, autoGeneratePassword);

  // Xử lý submit form
  const handleSubmit = (values: unknown) => {
    try {
      // Reset form errors
      setFormErrors({});

      // Type assertion to the correct type
      const formValues = values as CreateUserFormValues;

      // Prepare data for API
      const submitData = {
        email: formValues.email,
        employeeId: employeeId || formValues.employeeId,
        autoGeneratePassword,
        // Chỉ gửi username và password nếu không auto generate
        ...(autoGeneratePassword ? {} : {
          username: formValues.username,
          password: formValues.password,
          fullName: formValues.fullName,
        }),
      };

      // Call API to create user
      createUserMutation.mutate(submitData, {
        onSuccess: () => {
          // Show success notification
          const message = autoGeneratePassword
            ? t('hrm:employee.messages.userCreatedWithAutoPassword', 'Tài khoản người dùng đã được tạo và mật khẩu đã được gửi qua email')
            : t('hrm:employee.messages.userCreated', 'Tài khoản người dùng đã được tạo thành công');

          NotificationUtil.success({ message });

          if (onSuccess) {
            onSuccess();
          }
        },
        onError: (error: any) => {
          console.log('API Error:', error?.response?.data);

          // Handle specific error codes
          const errorData = error?.response?.data;
          const errorCode = errorData?.code;
          const errorMessage = errorData?.message;

          // Check for specific error codes and map to form fields
          if (errorCode === 13029) {
            // Email already exists error
            setFormErrors({
              email: errorMessage || t('hrm:employee.errors.emailExists', 'Email đã được sử dụng'),
            });
            return; // Don't show notification for field-specific errors
          }

          // Add more specific error code mappings as needed
          if (errorCode === 13030) {
            // Username already exists error (example)
            setFormErrors({
              username:
                errorMessage ||
                t('hrm:employee.errors.usernameExists', 'Tên đăng nhập đã được sử dụng'),
            });
            return;
          }

          // Handle other field-specific errors from API
          if (errorData?.errors) {
            setFormErrors(errorData.errors);
            return; // Don't show notification for field-specific errors
          }

          // Show notification for general errors
          if (errorMessage) {
            NotificationUtil.error({
              message: errorMessage,
            });
          } else {
            // Show generic error notification
            NotificationUtil.error({
              message: t(
                'hrm:employee.messages.userCreateError',
                'Có lỗi xảy ra khi tạo tài khoản người dùng'
              ),
            });
          }
        },
      });
    } catch (error) {
      console.error('Error submitting form:', error);
      NotificationUtil.error({
        message: t('common:error.unexpected', 'Đã xảy ra lỗi không mong muốn'),
      });
    }
  };

  return (
    <Card>
      <Form
        ref={formRef as unknown as React.RefObject<FormRef<FieldValues>>}
        schema={schema}
        onSubmit={handleSubmit}
        className="space-y-6"
      >
        <div className="p-6">
          <h2 className="text-xl font-semibold mb-6">
            {t('hrm:employee.form.createUserTitle', 'Tạo tài khoản người dùng cho nhân viên')}
          </h2>

          <div className="space-y-6">
            {/* Hiển thị thông tin nhân viên đã chọn */}
            {employeeData && (
              <div className="p-4 bg-background text-foreground rounded-lg">
                <Typography variant="subtitle2" className="mb-2">
                  {t('hrm:employee.form.selectedEmployee', 'Nhân viên đã chọn')}
                </Typography>
                <div className="space-y-1">
                  <Typography variant="body2">
                    <strong>{t('hrm:employee.form.employeeName', 'Tên nhân viên')}:</strong> {employeeData.employeeName}
                  </Typography>
                  <Typography variant="body2">
                    <strong>{t('hrm:employee.form.employeeCode', 'Mã nhân viên')}:</strong> {employeeData.employeeCode}
                  </Typography>
                  {employeeData.department && (
                    <Typography variant="body2">
                      <strong>{t('hrm:employee.form.department', 'Phòng ban')}:</strong> {employeeData.department.name}
                    </Typography>
                  )}
                </div>
              </div>
            )}

            {/* Trường chọn nhân viên - chỉ hiển thị khi không có employeeId */}
            {!employeeId && (
              <FormItem name="employeeId" label={t('hrm:employee.form.employee', 'Nhân viên')} required>
                <AsyncSelectWithPagination
                  loadOptions={loadEmployeesForAsyncSelect}
                  onChange={(value: string | string[] | number | number[] | undefined) => {
                    if (formRef.current && value) {
                      formRef.current.setValues({ employeeId: Number(value) });
                    }
                  }}
                  debounceTime={300}
                  noOptionsMessage={t('common:noResults', 'Không tìm thấy kết quả')}
                  loadingMessage={t('common:loading', 'Đang tìm kiếm...')}
                  autoLoadInitial={true}
                  fullWidth
                />
              </FormItem>
            )}

            <FormItem name="email" label={t('hrm:employee.form.email', 'Email')} required>
              <Input type="email" fullWidth />
            </FormItem>

            {/* Checkbox tự động tạo mật khẩu */}
            <div className="space-y-4">
              <Checkbox
                label={t('hrm:employee.form.autoGeneratePassword', 'Tự động tạo mật khẩu và gửi thông báo qua email')}
                checked={autoGeneratePassword}
                onChange={setAutoGeneratePassword}
                variant="filled"
                color="primary"
              />
            </div>

            {/* Các trường mật khẩu - chỉ hiển thị khi không auto generate */}
            {!autoGeneratePassword && (
              <FormGrid columns={2}>
                <FormItem
                  name="password"
                  label={t('hrm:employee.form.password', 'Mật khẩu')}
                  required
                >
                  <Input
                    type={showPassword ? 'text' : 'password'}
                    rightIcon={
                      <div className="cursor-pointer" onClick={() => setShowPassword(!showPassword)}>
                        <Icon name={showPassword ? 'eye-off' : 'eye'} size="sm" />
                      </div>
                    }
                    fullWidth
                  />
                </FormItem>

                <FormItem
                  name="confirmPassword"
                  label={t('hrm:employee.form.confirmPassword', 'Xác nhận mật khẩu')}
                  required
                >
                  <Input
                    type={showConfirmPassword ? 'text' : 'password'}
                    rightIcon={
                      <div
                        className="cursor-pointer"
                        onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                      >
                        <Icon name={showConfirmPassword ? 'eye-off' : 'eye'} size="sm" />
                      </div>
                    }
                    fullWidth
                  />
                </FormItem>
              </FormGrid>
            )}
          </div>

          <div className="flex justify-end space-x-3 mt-6">
            {onCancel && (
              <Button variant="outline" onClick={onCancel} disabled={createUserMutation.isPending}>
                {t('common:cancel', 'Hủy')}
              </Button>
            )}
            <Button
              type="submit"
              isLoading={createUserMutation.isPending}
              disabled={createUserMutation.isPending}
            >
              {t('common:save', 'Lưu')}
            </Button>
          </div>
        </div>
      </Form>
    </Card>
  );
};

export default CreateUserForEmployeeForm;
