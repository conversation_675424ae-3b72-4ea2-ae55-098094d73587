import { PermissionEnum } from '@/shared/types/permission';

// Giao diện cho ModernMenuItem với trường permission
export interface ModernMenuItem {
  id: string;
  label: string;
  path: string;
  icon: string;
  keywords: string[];
  permission: PermissionEnum;
}

/**
 * <PERSON>h sách menu cho người dùng thông thường
 * Chỉ chứa các module có trong HomePage
 */
export const userMenuItems: ModernMenuItem[] = [
  {
    id: 'home',
    label: 'common:home',
    path: '/',
    icon: 'home',
    keywords: ['trang chủ', 'home', 'main', 'dashboard', 'trang chinh'],
    permission: PermissionEnum.DASHBOARD_VIEW_OWN,
  },
  {
    id: 'okrs',
    label: 'OKRs',
    path: '/okrs',
    icon: 'award',
    keywords: [
      'okrs',
      'mục tiêu',
      'muc tieu',
      'objectives',
      'key results',
      'kết quả then chốt',
      'ket qua then chot',
    ],
    permission: PermissionEnum.DASHBOARD_VIEW_OWN, // Sử dụng quyền cơ bản cho OKR
  },
  {
    id: 'todolist',
    label: 'Todolist',
    path: '/todolist',
    icon: 'check',
    keywords: [
      'todolist',
      'công việc',
      'cong viec',
      'dự án',
      'du an',
      'tasks',
      'todo',
      'việc cần làm',
      'viec can lam',
    ],
    permission: PermissionEnum.PROJECT_VIEW_LIST,
  },
  {
    id: 'calendar',
    label: 'home:modules.calendar.title',
    path: '/calendar',
    icon: 'calendar',
    keywords: [
      'calendar',
      'lịch',
      'lich',
      'lịch làm việc',
      'lich lam viec',
      'sự kiện',
      'su kien',
      'events',
    ],
    permission: PermissionEnum.DASHBOARD_VIEW_OWN, // Sử dụng quyền cơ bản cho calendar
  },
  {
    id: 'marketing',
    label: 'Marketing',
    path: '/marketing',
    icon: 'campaign',
    keywords: [
      'marketing',
      'chiến dịch',
      'chien dich',
      'campaign',
      'quảng cáo',
      'quang cao',
      'nội dung',
      'noi dung',
    ],
    permission: PermissionEnum.DASHBOARD_VIEW_OWN, // Sử dụng quyền cơ bản cho marketing
  },
  {
    id: 'crm',
    label: 'CRM',
    path: '/crm',
    icon: 'users',
    keywords: [
      'crm',
      'khách hàng',
      'khach hang',
      'customers',
      'clients',
      'mối quan hệ',
      'moi quan he',
      'relationships',
    ],
    permission: PermissionEnum.CUSTOMER_VIEW_LIST,
  },
  {
    id: 'hrm',
    label: 'HRM',
    path: '/hrm',
    icon: 'user',
    keywords: [
      'hrm',
      'nhân sự',
      'nhan su',
      'human resources',
      'tuyển dụng',
      'tuyen dung',
      'nhân viên',
      'nhan vien',
      'employees',
    ],
    permission: PermissionEnum.EMPLOYEE_VIEW_LIST,
  },
  {
    id: 'integrations',
    label: 'Tích hợp',
    path: '/integrations',
    icon: 'integration',
    keywords: [],
    permission: PermissionEnum.EMPLOYEE_VIEW_LIST,
  },
];

/**
 * Danh sách menu cho quản trị viên
 */
export const adminMenuItems: ModernMenuItem[] = [
  {
    id: 'admin-dashboard',
    label: 'admin:dashboard',
    path: '/admin',
    icon: 'home',
    keywords: ['admin', 'dashboard', 'trang quản trị', 'trang quan tri', 'quản trị', 'quan tri'],
    permission: PermissionEnum.DASHBOARD_VIEW_MANAGEMENT,
  },
  {
    id: 'admin-users',
    label: 'admin:users',
    path: '/admin/users',
    icon: 'user',
    keywords: ['users', 'người dùng', 'nguoi dung', 'khách hàng', 'khach hang', 'customers'],
    permission: PermissionEnum.USER_VIEW_LIST,
  },
  {
    id: 'admin-roles',
    label: 'admin:roles',
    path: '/admin/roles',
    icon: 'lock',
    keywords: ['roles', 'vai trò', 'vai tro', 'permissions', 'quyền', 'quyen'],
    permission: PermissionEnum.ROLE_VIEW_LIST,
  },
  {
    id: 'admin-permissions',
    label: 'admin:permissions',
    path: '/admin/permissions',
    icon: 'eye',
    keywords: ['permissions', 'quyền', 'quyen', 'quyền hạn', 'quyen han', 'access'],
    permission: PermissionEnum.PERMISSION_VIEW_LIST,
  },
  {
    id: 'admin-employees',
    label: 'admin:employees',
    path: '/admin/employees',
    icon: 'users',
    keywords: [
      'employees',
      'nhân viên',
      'nhan vien',
      'staff',
      'quản lý nhân viên',
      'quan ly nhan vien',
    ],
    permission: PermissionEnum.EMPLOYEE_VIEW_LIST,
  },
  {
    id: 'admin-departments',
    label: 'admin:departments',
    path: '/admin/departments',
    icon: 'building',
    keywords: ['departments', 'phòng ban', 'phong ban', 'teams', 'nhóm', 'nhom'],
    permission: PermissionEnum.DEPARTMENT_VIEW_LIST,
  },
  {
    id: 'admin-projects',
    label: 'admin:projects',
    path: '/admin/projects',
    icon: 'folder',
    keywords: [
      'projects',
      'dự án',
      'du an',
      'project management',
      'quản lý dự án',
      'quan ly du an',
    ],
    permission: PermissionEnum.PROJECT_VIEW_LIST,
  },
  {
    id: 'admin-customers',
    label: 'admin:customers',
    path: '/admin/customers',
    icon: 'users',
    keywords: ['customers', 'khách hàng', 'khach hang', 'clients', 'client management'],
    permission: PermissionEnum.CUSTOMER_VIEW_LIST,
  },
  {
    id: 'admin-sales-orders',
    label: 'admin:sales-orders',
    path: '/admin/sales-orders',
    icon: 'shopping-cart',
    keywords: ['sales orders', 'đơn hàng', 'don hang', 'orders', 'sales management'],
    permission: PermissionEnum.SALES_ORDER_VIEW_LIST,
  },
  {
    id: 'admin-purchase-orders',
    label: 'admin:purchase-orders',
    path: '/admin/purchase-orders',
    icon: 'shopping-cart',
    keywords: ['purchase orders', 'đơn mua hàng', 'don mua hang', 'purchases', 'mua hàng'],
    permission: PermissionEnum.PURCHASE_ORDER_VIEW_LIST,
  },
  {
    id: 'admin-inventory',
    label: 'admin:inventory',
    path: '/admin/inventory',
    icon: 'server',
    keywords: ['inventory', 'kho', 'ton kho', 'stock', 'hàng hóa', 'hang hoa'],
    permission: PermissionEnum.INVENTORY_VIEW_ITEMS,
  },
  {
    id: 'admin-warehouses',
    label: 'admin:warehouses',
    path: '/admin/warehouses',
    icon: 'building',
    keywords: ['warehouses', 'kho hàng', 'kho hang', 'storage', 'quản lý kho', 'quan ly kho'],
    permission: PermissionEnum.WAREHOUSE_MANAGE,
  },
  {
    id: 'admin-suppliers',
    label: 'admin:suppliers',
    path: '/admin/suppliers',
    icon: 'users',
    keywords: ['suppliers', 'nhà cung cấp', 'nha cung cap', 'vendors', 'quản lý nhà cung cấp'],
    permission: PermissionEnum.SUPPLIER_VIEW_LIST,
  },
  {
    id: 'admin-invoices',
    label: 'admin:invoices',
    path: '/admin/invoices',
    icon: 'document',
    keywords: ['invoices', 'hóa đơn', 'hoa don', 'billing', 'bills'],
    permission: PermissionEnum.INVOICE_SALES_VIEW_LIST,
  },
  {
    id: 'admin-payments',
    label: 'admin:payments',
    path: '/admin/payments',
    icon: 'payment',
    keywords: ['payments', 'thanh toán', 'thanh toan', 'transactions', 'giao dịch', 'giao dich'],
    permission: PermissionEnum.PAYMENT_RECEIVABLE_VIEW_LIST,
  },
  {
    id: 'admin-timesheets',
    label: 'admin:timesheets',
    path: '/admin/timesheets',
    icon: 'calendar',
    keywords: ['timesheets', 'chấm công', 'cham cong', 'time tracking', 'quản lý chấm công'],
    permission: PermissionEnum.TIMESHEET_VIEW_ALL,
  },
  {
    id: 'admin-leave',
    label: 'admin:leave',
    path: '/admin/leave',
    icon: 'calendar',
    keywords: ['leave', 'nghỉ phép', 'nghi phep', 'vacation', 'absence management'],
    permission: PermissionEnum.LEAVE_VIEW_ALL,
  },
  {
    id: 'admin-reports',
    label: 'admin:reports',
    path: '/admin/reports',
    icon: 'chart',
    keywords: ['reports', 'báo cáo', 'bao cao', 'analytics', 'phân tích', 'phan tich'],
    permission: PermissionEnum.REPORT_VIEW_STANDARD,
  },
  {
    id: 'admin-okr',
    label: 'admin:okr',
    path: '/admin/okr',
    icon: 'award',
    keywords: ['okr', 'mục tiêu', 'muc tieu', 'objectives', 'key results'],
    permission: PermissionEnum.OKR_CREATE_COMPANY,
  },
  {
    id: 'admin-audit-logs',
    label: 'admin:audit-logs',
    path: '/admin/audit-logs',
    icon: 'list',
    keywords: ['audit logs', 'nhật ký', 'nhat ky', 'hoạt động', 'hoat dong', 'logs'],
    permission: PermissionEnum.AUDIT_LOG_VIEW,
  },
  {
    id: 'admin-settings',
    label: 'admin:settings',
    path: '/admin/settings',
    icon: 'settings',
    keywords: ['settings', 'cài đặt', 'cai dat', 'thiết lập', 'thiet lap', 'config'],
    permission: PermissionEnum.SYSTEM_SETTING_MANAGE_GENERAL,
  },
];

/**
 * Lọc menu items dựa trên danh sách quyền
 * @param menuItems Danh sách menu items cần lọc
 * @param permissions Danh sách quyền của người dùng
 * @returns Danh sách menu items mà người dùng có quyền truy cập
 */
export const getMenuItemsByPermissions = (
  menuItems: ModernMenuItem[],
  permissions: PermissionEnum[]
): ModernMenuItem[] => {
  return menuItems.filter(item => permissions.includes(item.permission));
};
